import { Suspense } from "react";
import { Metadata } from "next";
import { Shell } from "@/components/shell";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { getAllPaginatedSubmissionsQuery } from "@/server/actions/submissions/queries";
import { SubmissionsTable } from "./_components/submissions-table";
import { pageConfig } from "./_constants/page-config";
import { SearchParams } from "@/types/data-table";
import { z } from "zod";

const searchParamsSchema = z.object({
    page: z.coerce.number().default(1),
    per_page: z.coerce.number().default(10),
    sort: z.string().optional(),
    status: z.string().optional(),
    submitter: z.string().optional(),
    from: z.string().optional(),
    to: z.string().optional(),
});

export const metadata: Metadata = {
    title: pageConfig.title,
    description: pageConfig.description,
};

interface SubmissionsPageProps {
    searchParams: SearchParams;
}

export default function SubmissionsPage({ searchParams }: SubmissionsPageProps) {
    const search = searchParamsSchema.parse(searchParams);

    const submissionsPromise = getAllPaginatedSubmissionsQuery({
        page: search.page,
        per_page: search.per_page,
        sort: search.sort,
        status: search.status as "Pending" | "Under Review" | "Approved" | "Rejected" | "Needs Changes" | undefined,
        submitter: search.submitter,
        from: search.from,
        to: search.to,
    });

    return (
        <Shell variant="sidebar">
            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div className="space-y-1">
                        <h1 className="text-2xl font-semibold tracking-tight">
                            {pageConfig.title}
                        </h1>
                        <p className="text-muted-foreground">
                            {pageConfig.description}
                        </p>
                    </div>
                </div>

                {/* Submissions Table */}
                <Suspense
                    fallback={
                        <DataTableSkeleton
                            columnCount={7}
                            searchableColumnCount={2}
                            filterableColumnCount={1}
                            cellWidths={["10rem", "20rem", "15rem", "10rem", "12rem", "10rem", "8rem"]}
                            shrinkZero
                        />
                    }
                >
                    <SubmissionsTable submissionsPromise={submissionsPromise} />
                </Suspense>
            </div>
        </Shell>
    );
}