import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Icons } from "@/components/ui/icons";
import { Users, ExternalLink } from "lucide-react";
import { Project } from "@/lib/projects";

interface ProjectsGridProps {
  projects: Project[];
}

function ProjectCard({ project }: { project: Project }) {
  return (
    <Card className="h-full flex flex-col hover:shadow-lg transition-shadow group">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <Link href={`/projects/${project.id}`}>
              <CardTitle className="text-lg mb-2 group-hover:text-primary transition-colors cursor-pointer">
                {project.name}
              </CardTitle>
            </Link>
            <CardDescription className="text-sm line-clamp-2">
              {project.description}
            </CardDescription>
          </div>
          {project.featured && (
            <Badge variant="secondary" className="ml-2">
              <Icons.star className="w-3 h-3 mr-1" />
              Featured
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="flex-1">
        <div className="space-y-3">
          {/* Category */}
          <div>
            <Link 
              href={`/categories/${encodeURIComponent(project.category.toLowerCase())}`}
              className="text-sm text-primary hover:underline"
            >
              {project.category}
            </Link>
          </div>
          
          {/* Rating and Users */}
          {(project.rating || project.users) && (
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              {project.rating && (
                <div className="flex items-center gap-1">
                  <Icons.star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span>{project.rating}</span>
                </div>
              )}
              {project.users && (
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span>{project.users.toLocaleString()}</span>
                </div>
              )}
            </div>
          )}
          
          {/* Tags */}
          <div className="flex flex-wrap gap-1">
            {project.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {project.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{project.tags.length - 3}
              </Badge>
            )}
          </div>
          
          {/* Pricing */}
          <div className="flex justify-end">
            <Badge 
              variant={project.pricing === 'free' ? 'default' : 'secondary'}
              className="capitalize"
            >
              {project.pricing}
            </Badge>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="pt-0">
        <div className="flex gap-2 w-full">
          <Button asChild className="flex-1" variant="outline">
            <Link href={`/projects/${project.id}`}>
              View Details
            </Link>
          </Button>
          {project.website && (
            <Button asChild size="sm">
               <Link href={project.website} target="_blank" rel="noopener noreferrer">
                 <ExternalLink className="w-4 h-4 mr-1" />
                 Visit
               </Link>
             </Button>
          )}
          {project.github && (
            <Button variant="outline" size="sm" asChild>
              <Link href={project.github} target="_blank" rel="noopener noreferrer">
                <Icons.gitHub className="w-4 h-4" />
              </Link>
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}

export function ProjectsGrid({ projects }: ProjectsGridProps) {
  if (!projects || projects.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-2xl font-semibold mb-4">No projects found</h3>
        <p className="text-muted-foreground mb-6">
          We couldn't find any projects matching your criteria.
        </p>
        <Button>
          <Icons.plus className="w-4 h-4 mr-2" />
          Submit a Project
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map((project) => (
        <ProjectCard key={project.id} project={project} />
      ))}
    </div>
  );
}

export function ProjectsGridSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <Card key={i} className="h-80">
          <CardHeader>
            <div className="space-y-2">
              <div className="h-6 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
              <div className="flex gap-2">
                <div className="h-6 bg-gray-200 rounded animate-pulse w-16" />
                <div className="h-6 bg-gray-200 rounded animate-pulse w-20" />
              </div>
              <div className="flex justify-between">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-12" />
                <div className="h-6 bg-gray-200 rounded animate-pulse w-16" />
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <div className="h-10 bg-gray-200 rounded animate-pulse w-full" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}