import { StarIcon, GitForkIcon, ClockIcon } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";

interface ProjectStatsCompactProps {
    stars?: number | null;
    forks?: number | null;
    lastCommit?: Date | null;
    className?: string;
}

/**
 * Component hiển thị GitHub stats trong format compact và cân đối
 * Thiết kế đặc biệt cho project cards
 */
export function ProjectStatsCompact({ 
    stars, 
    forks, 
    lastCommit, 
    className 
}: ProjectStatsCompactProps) {
    const formatNumber = (num: number | null | undefined): string => {
        if (!num) return "0";
        if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
        if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
        return num.toLocaleString();
    };

    const lastUpdate = lastCommit 
        ? formatDistanceToNow(new Date(lastCommit), { addSuffix: true })
        : null;

    return (
        <div className={cn("flex items-center gap-4 text-xs text-muted-foreground", className)}>
            {/* Stars */}
            {stars !== null && stars !== undefined && (
                <div className="flex items-center gap-1">
                    <StarIcon className="h-3 w-3 fill-current" />
                    <span className="font-medium">{formatNumber(stars)}</span>
                </div>
            )}
            
            {/* Forks */}
            {forks !== null && forks !== undefined && (
                <div className="flex items-center gap-1">
                    <GitForkIcon className="h-3 w-3" />
                    <span className="font-medium">{formatNumber(forks)}</span>
                </div>
            )}
            
            {/* Last Update */}
            {lastUpdate && (
                <div className="flex items-center gap-1 text-xs">
                    <ClockIcon className="h-3 w-3" />
                    <span className="truncate max-w-20">{lastUpdate.replace(' ago', '')}</span>
                </div>
            )}
        </div>
    );
}

/**
 * Component hiển thị chỉ stars với design đẹp
 */
export function StarBadge({ 
    count, 
    className 
}: { 
    count: number | null | undefined; 
    className?: string;
}) {
    if (!count) return null;

    const formatCount = (num: number): string => {
        if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
        if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
        return num.toLocaleString();
    };

    return (
        <div className={cn(
            "inline-flex items-center gap-1 px-2 py-1 rounded-md bg-yellow-50 text-yellow-700 border border-yellow-200 text-xs font-medium",
            "dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30",
            className
        )}>
            <StarIcon className="h-3 w-3 fill-current" />
            <span>{formatCount(count)}</span>
        </div>
    );
}
