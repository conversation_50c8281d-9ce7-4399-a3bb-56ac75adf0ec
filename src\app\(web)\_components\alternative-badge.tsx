import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface AlternativeBadgeProps {
    alternatives: string[];
    className?: string;
    variant?: "default" | "compact";
}

/**
 * Component hiển thị thông tin "Alternative to" cho projects
 * Hỗ trợ hiển thị nhiều alternatives và variant khác nhau
 */
export function AlternativeBadge({ 
    alternatives, 
    className,
    variant = "default" 
}: AlternativeBadgeProps) {
    if (!alternatives || alternatives.length === 0) {
        return null;
    }

    if (variant === "compact") {
        return (
            <div className={cn("flex items-center gap-2 text-xs text-muted-foreground", className)}>
                <span>Alternative to:</span>
                <div className="flex items-center gap-1">
                    {alternatives.slice(0, 2).map((alt, index) => (
                        <Badge
                            key={index}
                            variant="outline"
                            className="text-xs bg-primary/5 text-primary border-primary/20 hover:bg-primary/10"
                        >
                            {alt}
                        </Badge>
                    ))}
                    {alternatives.length > 2 && (
                        <span className="text-xs text-muted-foreground">
                            +{alternatives.length - 2} more
                        </span>
                    )}
                </div>
            </div>
        );
    }

    return (
        <div className={cn("space-y-2", className)}>
            <p className="text-sm text-muted-foreground">
                Alternative to:
            </p>
            <div className="flex flex-wrap gap-1">
                {alternatives.slice(0, 3).map((alt, index) => (
                    <Badge 
                        key={index} 
                        variant="outline" 
                        className="text-xs bg-primary/5 text-primary border-primary/20 hover:bg-primary/10"
                    >
                        {alt}
                    </Badge>
                ))}
                {alternatives.length > 3 && (
                    <Badge 
                        variant="outline" 
                        className="text-xs bg-muted/50 text-muted-foreground"
                    >
                        +{alternatives.length - 3} more
                    </Badge>
                )}
            </div>
        </div>
    );
}

/**
 * Helper function để extract alternatives từ tags hoặc description
 */
export function extractAlternatives(project: {
    tags?: string[] | null;
    description?: string;
    name: string;
}): string[] {
    const alternatives: string[] = [];

    // Kiểm tra trong tags
    if (project.tags) {
        const altTags = project.tags.filter(tag => 
            tag.toLowerCase().includes("alternative") ||
            tag.toLowerCase().includes("replace")
        );
        
        // Extract tên từ tags như "discord-alternative", "slack-alternative"
        altTags.forEach(tag => {
            const match = tag.match(/^(.+)-(alternative|replace)$/i);
            if (match) {
                alternatives.push(match[1].charAt(0).toUpperCase() + match[1].slice(1));
            }
        });
    }

    // Kiểm tra trong description cho pattern "alternative to X"
    if (project.description) {
        const altMatches = project.description.match(/alternative to ([^,.]+)/gi);
        if (altMatches) {
            altMatches.forEach(match => {
                const alt = match.replace(/alternative to /i, "").trim();
                if (alt && !alternatives.includes(alt)) {
                    alternatives.push(alt);
                }
            });
        }
    }

    // Hardcoded mapping cho một số projects phổ biến
    const knownAlternatives: Record<string, string[]> = {
        "discourse": ["Discord", "Slack"],
        "nextcloud": ["Google Drive", "Dropbox"],
        "gitlab": ["GitHub"],
        "matrix": ["Discord", "Slack"],
        "jitsi": ["Zoom", "Teams"],
        "rocket.chat": ["Slack", "Teams"],
        "mattermost": ["Slack", "Teams"],
        "element": ["Discord", "Slack"],
        "signal": ["WhatsApp", "Telegram"],
        "brave": ["Chrome", "Safari"],
        "firefox": ["Chrome", "Safari"],
        "libreoffice": ["Microsoft Office"],
        "gimp": ["Photoshop"],
        "blender": ["Maya", "3ds Max"],
        "audacity": ["Adobe Audition"],
        "obs": ["XSplit", "Streamlabs"],
        "vlc": ["Windows Media Player"],
        "thunderbird": ["Outlook"],
        "filezilla": ["WinSCP"],
        "7zip": ["WinRAR"],
    };

    const projectKey = project.name.toLowerCase().replace(/\s+/g, "");
    if (knownAlternatives[projectKey]) {
        knownAlternatives[projectKey].forEach(alt => {
            if (!alternatives.includes(alt)) {
                alternatives.push(alt);
            }
        });
    }

    return alternatives.slice(0, 5); // Giới hạn tối đa 5 alternatives
}
