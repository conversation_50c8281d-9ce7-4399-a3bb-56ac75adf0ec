import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { ArrowLeft, ExternalLink, Star, Users, Calendar, Tag } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { getProjectById, getProjects } from "@/server/actions/projects";

interface ProjectDetailPageProps {
  params: {
    id: string;
  };
}

/**
 * Tạo metadata động cho trang chi tiết project
 */
export async function generateMetadata({ params }: ProjectDetailPageProps): Promise<Metadata> {
  const project = await getProjectById(params.id);
  
  if (!project) {
    return {
      title: "Project Not Found",
      description: "The requested project could not be found."
    };
  }

  return {
    title: `${project.name} | AI Tools Directory`,
    description: project.description,
    openGraph: {
      title: project.name,
      description: project.description,
      images: project.logo ? [{ url: project.logo }] : [],
    },
  };
}

/**
 * Tạo static params cho tất cả projects
 */
export async function generateStaticParams() {
  const projects = await getProjects();
  return projects.map((project) => ({
    id: project.id,
  }));
}

/**
 * Component hiển thị thông tin chi tiết project
 */
function ProjectHeader({ project }: { project: any }) {
  return (
    <div className="space-y-6">
      {/* Breadcrumb và nút quay lại */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Link href="/" className="hover:text-foreground transition-colors">
          Home
        </Link>
        <span>/</span>
        <Link href="/categories" className="hover:text-foreground transition-colors">
          Categories
        </Link>
        <span>/</span>
        <Link 
          href={`/categories/${encodeURIComponent(project.category)}`}
          className="hover:text-foreground transition-colors"
        >
          {project.category}
        </Link>
        <span>/</span>
        <span className="text-foreground">{project.name}</span>
      </div>

      {/* Nút quay lại */}
      <Link href="/categories" className="inline-flex items-center gap-2 text-sm hover:underline">
        <ArrowLeft className="h-4 w-4" />
        Back to Categories
      </Link>

      {/* Header chính */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Logo/Image */}
        {project.logo && (
          <div className="flex-shrink-0">
            <div className="w-24 h-24 lg:w-32 lg:h-32 rounded-xl overflow-hidden border bg-muted">
              <Image
                src={project.logo}
                alt={project.name}
                width={128}
                height={128}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        )}

        {/* Thông tin chính */}
        <div className="flex-1 space-y-4">
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold tracking-tight">{project.name}</h1>
            <p className="text-xl text-muted-foreground mt-2">{project.description}</p>
          </div>

          {/* Tags và category */}
          <div className="flex flex-wrap gap-2">
            <Badge variant="secondary" className="gap-1">
              <Tag className="h-3 w-3" />
              {project.category}
            </Badge>
            {project.tags?.map((tag: string) => (
              <Badge key={tag} variant="outline">
                {tag}
              </Badge>
            ))}
          </div>

          {/* Stats */}
          <div className="flex flex-wrap gap-6 text-sm text-muted-foreground">
            {project.githubStars && (
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">{project.githubStars.toLocaleString()}</span>
                <span>stars</span>
              </div>
            )}
            {project.viewCount && (
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>{project.viewCount.toLocaleString()} views</span>
              </div>
            )}
            {project.createdAt && (
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>Added {new Date(project.createdAt).toLocaleDateString()}</span>
              </div>
            )}
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-wrap gap-3">
            {project.websiteUrl && (
              <Button asChild size="lg">
                <a href={project.websiteUrl} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Visit Website
                </a>
              </Button>
            )}
            {project.demoUrl && (
              <Button variant="outline" asChild size="lg">
                <a href={project.demoUrl} target="_blank" rel="noopener noreferrer">
                  Try Demo
                </a>
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Component hiển thị thông tin chi tiết và tính năng
 */
function ProjectDetails({ project }: { project: any }) {
  return (
    <div className="grid lg:grid-cols-3 gap-8">
      {/* Nội dung chính */}
      <div className="lg:col-span-2 space-y-8">
        {/* Screenshots */}
        {project.screenshots && project.screenshots.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Screenshots</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {project.screenshots.map((screenshot: string, index: number) => (
                  <div key={index} className="relative aspect-video rounded-lg overflow-hidden border">
                    <Image
                      src={screenshot}
                      alt={`${project.name} screenshot ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Mô tả chi tiết */}
        <Card>
          <CardHeader>
            <CardTitle>About {project.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-gray dark:prose-invert max-w-none">
              <p className="text-muted-foreground leading-relaxed">
                {project.description}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Tính năng chính */}
        {project.features && project.features.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Key Features</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {project.features.map((feature: string, index: number) => (
                  <li key={index} className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0" />
                    <span className="text-muted-foreground">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Thông tin cơ bản */}
        <Card>
          <CardHeader>
            <CardTitle>Project Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Category */}
            <div>
              <h4 className="font-medium mb-2">Category</h4>
              <Badge variant="secondary">{project.category}</Badge>
            </div>
            
            {/* Primary Language */}
            {project.primaryLanguage && (
              <div>
                <h4 className="font-medium mb-2">Primary Language</h4>
                <p className="text-sm text-muted-foreground">{project.primaryLanguage}</p>
              </div>
            )}

            {/* Languages */}
            {project.languages && project.languages.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Languages</h4>
                <div className="flex flex-wrap gap-1">
                  {project.languages.map((lang: string) => (
                    <Badge key={lang} variant="outline" className="text-xs">
                      {lang}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* License */}
            {project.license && (
              <div>
                <h4 className="font-medium mb-2">License</h4>
                <p className="text-sm text-muted-foreground">{project.license}</p>
              </div>
            )}

            {/* Open Source */}
            {project.isOpenSource && (
              <div>
                <h4 className="font-medium mb-2">Open Source</h4>
                <Badge variant="outline" className="text-green-600">
                  Yes
                </Badge>
              </div>
            )}

            {/* Last Updated */}
            <div>
              <h4 className="font-medium mb-2">Last Updated</h4>
              <p className="text-sm text-muted-foreground">
                {new Date(project.updatedAt).toLocaleDateString()}
              </p>
            </div>

            <Separator />

            {/* Links */}
            <div className="space-y-2">
              {project.websiteUrl && (
                <a 
                  href={project.websiteUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-sm hover:underline"
                >
                  <ExternalLink className="h-4 w-4" />
                  Official Website
                </a>
              )}
              {project.githubUrl && (
                <a 
                  href={project.githubUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-sm hover:underline"
                >
                  <ExternalLink className="h-4 w-4" />
                  GitHub Repository
                </a>
              )}
              {project.documentationUrl && (
                <a 
                  href={project.documentationUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-sm hover:underline"
                >
                  <ExternalLink className="h-4 w-4" />
                  Documentation
                </a>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Related Projects */}
        <Card>
          <CardHeader>
            <CardTitle>Related Tools</CardTitle>
            <CardDescription>
              Other tools in {project.category}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-4">
              <Link 
                href={`/categories/${encodeURIComponent(project.category)}`}
                className="text-sm text-primary hover:underline"
              >
                View all {project.category} tools →
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

/**
 * Trang chi tiết project chính
 */
export default async function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  const project = await getProjectById(params.id);

  if (!project) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="space-y-8">
        <ProjectHeader project={project} />
        <Separator />
        <ProjectDetails project={project} />
      </div>
    </div>
  );
}