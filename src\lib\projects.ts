export interface Project {
  id: string;
  name: string;
  description: string;
  longDescription?: string;
  category: string;
  tags: string[];
  website?: string;
  github?: string;
  demo?: string;
  documentation?: string;
  image?: string;
  pricing: 'free' | 'freemium' | 'paid';
  rating?: number;
  reviews?: number;
  users?: number;
  featured?: boolean;
  features?: string[];
  useCases?: string[];
  platform?: string;
  language?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectFilters {
  search?: string;
  sort?: string;
  tags?: string[];
}

// Mock data for development
const mockProjects: Project[] = [
  {
    id: '1',
    name: 'ChatGPT',
    description: 'Advanced conversational AI that can help with writing, coding, analysis, and more.',
    longDescription: 'ChatGPT is a state-of-the-art conversational AI developed by OpenAI. It uses advanced natural language processing to understand and respond to human queries in a natural, helpful way. Whether you need help with writing, coding, analysis, creative tasks, or general questions, ChatGPT can assist you with detailed and contextual responses.',
    category: 'Natural Language Processing',
    tags: ['Conversational AI', 'Text Generation', 'API'],
    website: 'https://chat.openai.com',
    documentation: 'https://platform.openai.com/docs',
    image: '/api/placeholder/128/128',
    pricing: 'freemium',
    rating: 4.8,
    reviews: 125000,
    users: 100000000,
    featured: true,
    features: [
      'Natural language conversations',
      'Code generation and debugging',
      'Creative writing assistance',
      'Data analysis and insights',
      'Multiple language support',
      'API integration available'
    ],
    useCases: [
      'Customer support automation',
      'Content creation and editing',
      'Educational tutoring',
      'Programming assistance',
      'Research and analysis'
    ],
    platform: 'Web, API, Mobile',
    language: 'Multiple languages',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'Midjourney',
    description: 'AI-powered image generation tool that creates stunning artwork from text prompts.',
    longDescription: 'Midjourney is an independent research lab that produces an AI program that creates images from textual descriptions. It is one of the most popular AI art generators, known for its high-quality, artistic outputs and unique aesthetic style.',
    category: 'Computer Vision',
    tags: ['Image Generation', 'Art', 'Creative'],
    website: 'https://midjourney.com',
    documentation: 'https://docs.midjourney.com',
    image: '/api/placeholder/128/128',
    pricing: 'paid',
    rating: 4.7,
    reviews: 85000,
    users: 15000000,
    featured: true,
    features: [
      'High-quality image generation',
      'Artistic style variations',
      'Discord bot interface',
      'Upscaling and variations',
      'Commercial usage rights',
      'Community gallery'
    ],
    useCases: [
      'Digital art creation',
      'Concept art for games/films',
      'Marketing visuals',
      'Book illustrations',
      'Social media content'
    ],
    platform: 'Discord, Web',
    language: 'English',
    createdAt: new Date('2023-02-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '3',
    name: 'GitHub Copilot',
    description: 'AI pair programmer that helps you write code faster with intelligent suggestions.',
    category: 'Development Tools',
    tags: ['Code Generation', 'Programming', 'Productivity'],
    website: 'https://github.com/features/copilot',
    pricing: 'paid',
    rating: 4.6,
    featured: true,
    createdAt: new Date('2023-03-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '4',
    name: 'Stable Diffusion',
    description: 'Open-source deep learning text-to-image model for generating detailed images.',
    category: 'Computer Vision',
    tags: ['Image Generation', 'Open Source', 'Deep Learning'],
    github: 'https://github.com/Stability-AI/stablediffusion',
    pricing: 'free',
    rating: 4.5,
    featured: false,
    createdAt: new Date('2023-04-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '5',
    name: 'Hugging Face Transformers',
    description: 'State-of-the-art machine learning library for natural language processing.',
    category: 'Natural Language Processing',
    tags: ['Machine Learning', 'Open Source', 'API', 'Python'],
    website: 'https://huggingface.co/transformers',
    github: 'https://github.com/huggingface/transformers',
    pricing: 'free',
    rating: 4.9,
    featured: true,
    createdAt: new Date('2023-05-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '6',
    name: 'TensorFlow',
    description: 'End-to-end open source platform for machine learning and AI development.',
    category: 'Machine Learning',
    tags: ['Deep Learning', 'Open Source', 'Python', 'Research'],
    website: 'https://tensorflow.org',
    github: 'https://github.com/tensorflow/tensorflow',
    pricing: 'free',
    rating: 4.7,
    featured: true,
    createdAt: new Date('2023-06-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '7',
    name: 'Zapier',
    description: 'Automation platform that connects your apps and automates workflows.',
    category: 'Automation',
    tags: ['Workflow Automation', 'Integration', 'Productivity'],
    website: 'https://zapier.com',
    pricing: 'freemium',
    rating: 4.4,
    featured: false,
    createdAt: new Date('2023-07-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '8',
    name: 'Tableau',
    description: 'Data visualization and business intelligence platform with AI-powered insights.',
    category: 'Analytics',
    tags: ['Data Visualization', 'Business Intelligence', 'Analytics'],
    website: 'https://tableau.com',
    pricing: 'paid',
    rating: 4.3,
    featured: false,
    createdAt: new Date('2023-08-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

export async function getProjects(): Promise<Project[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  return mockProjects;
}

export async function getFeaturedProjects(): Promise<Project[]> {
  const projects = await getProjects();
  return projects.filter(project => project.featured);
}

export async function getProjectsByCategory(
  category: string,
  filters?: ProjectFilters
): Promise<Project[]> {
  const projects = await getProjects();
  
  let filteredProjects = projects.filter(
    project => project.category.toLowerCase() === category.toLowerCase()
  );

  // Apply search filter
  if (filters?.search) {
    const searchTerm = filters.search.toLowerCase();
    filteredProjects = filteredProjects.filter(
      project =>
        project.name.toLowerCase().includes(searchTerm) ||
        project.description.toLowerCase().includes(searchTerm) ||
        project.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  }

  // Apply tags filter
  if (filters?.tags && filters.tags.length > 0) {
    filteredProjects = filteredProjects.filter(project =>
      filters.tags!.some(filterTag =>
        project.tags.some(projectTag =>
          projectTag.toLowerCase().includes(filterTag.toLowerCase())
        )
      )
    );
  }

  // Apply sorting
  if (filters?.sort) {
    switch (filters.sort) {
      case 'newest':
        filteredProjects.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        break;
      case 'name':
        filteredProjects.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'rating':
        filteredProjects.sort((a, b) => (b.rating || 0) - (a.rating || 0));
        break;
      case 'popular':
      default:
        filteredProjects.sort((a, b) => {
          // Sort by featured first, then by rating
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          return (b.rating || 0) - (a.rating || 0);
        });
        break;
    }
  }

  return filteredProjects;
}

export async function getCategories(): Promise<string[]> {
  const projects = await getProjects();
  const categories = Array.from(new Set(projects.map(project => project.category)));
  return categories.sort();
}

export async function getProjectById(id: string): Promise<Project | null> {
  const projects = await getProjects();
  return projects.find(project => project.id === id) || null;
}

export async function searchProjects(query: string): Promise<Project[]> {
  const projects = await getProjects();
  const searchTerm = query.toLowerCase();
  
  return projects.filter(
    project =>
      project.name.toLowerCase().includes(searchTerm) ||
      project.description.toLowerCase().includes(searchTerm) ||
      project.category.toLowerCase().includes(searchTerm) ||
      project.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
}