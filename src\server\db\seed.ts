import { db } from "./index";
import { categories, projects } from "./schema";
import { eq } from "drizzle-orm";

/**
 * Seed data cho AI Tools Directory
 * Tạo categories và projects mẫu để test ứng dụng
 */
async function seed() {
  console.log("🌱 Bắt đầu seed database...");

  try {
    // Xóa dữ liệu cũ (nếu có)
    console.log("🗑️ Xóa dữ liệu cũ...");
    await db.delete(projects);
    await db.delete(categories);

    // Tạo categories mẫu
    console.log("📂 Tạo categories...");
    const categoryData = [
      {
        name: "Machine Learning",
        slug: "machine-learning",
        description: "Các công cụ và thư viện Machine Learning, bao gồm frameworks, algorithms và model training tools",
        icon: "🤖",
        color: "#FF6B6B",
        isActive: true,
      },
      {
        name: "Computer Vision",
        slug: "computer-vision",
        description: "Công cụ xử lý hình ảnh, object detection, image recognition và video analysis",
        icon: "👁️",
        color: "#4ECDC4",
        isActive: true,
      },
      {
        name: "Natural Language Processing",
        slug: "nlp",
        description: "Xử lý ngôn ngữ tự nhiên, text analysis, chatbots và language models",
        icon: "💬",
        color: "#45B7D1",
        isActive: true,
      },
      {
        name: "Data Science",
        slug: "data-science",
        description: "Công cụ phân tích dữ liệu, visualization, statistical analysis và data mining",
        icon: "📊",
        color: "#96CEB4",
        isActive: true,
      },
      {
        name: "Robotics",
        slug: "robotics",
        description: "Robotics frameworks, simulation tools, control systems và autonomous systems",
        icon: "🤖",
        color: "#FFEAA7",
        isActive: true,
      },
      {
        name: "Deep Learning",
        slug: "deep-learning",
        description: "Neural networks, deep learning frameworks và advanced AI models",
        icon: "🧠",
        color: "#DDA0DD",
        isActive: true,
      },
    ];

    const insertedCategories = await db.insert(categories).values(categoryData).returning();
    console.log(`✅ Đã tạo ${insertedCategories.length} categories`);

    // Tạo projects mẫu
    console.log("🚀 Tạo projects mẫu...");
    const projectData = [
      {
        name: "TensorFlow",
        slug: "tensorflow",
        description: "TensorFlow là một platform machine learning mã nguồn mở end-to-end. Nó có một hệ sinh thái toàn diện và linh hoạt của các công cụ, thư viện và tài nguyên cộng đồng cho phép các nhà nghiên cứu thúc đẩy nghiên cứu ML tiên tiến và các nhà phát triển dễ dàng xây dựng và triển khai các ứng dụng được hỗ trợ bởi ML.",
        shortDescription: "Platform machine learning mã nguồn mở end-to-end từ Google",
        githubUrl: "https://github.com/tensorflow/tensorflow",
        githubOwner: "tensorflow",
        githubRepo: "tensorflow",
        githubStars: 185000,
        githubForks: 74000,
        githubIssues: 2000,
        websiteUrl: "https://tensorflow.org",
        documentationUrl: "https://tensorflow.org/guide",
        categoryId: insertedCategories.find(c => c.slug === "machine-learning")!.id,
        tags: ["machine-learning", "deep-learning", "neural-networks", "python", "google"],
        primaryLanguage: "Python",
        languages: ["Python", "C++", "JavaScript"],
        license: "Apache-2.0",
        status: "Published",
        isFeatured: true,
        isOpenSource: true,
        viewCount: 15420,
        clickCount: 3240,
      },
      {
        name: "OpenCV",
        slug: "opencv",
        description: "OpenCV (Open Source Computer Vision Library) là một thư viện phần mềm computer vision và machine learning mã nguồn mở. OpenCV được xây dựng để cung cấp một cơ sở hạ tầng chung cho các ứng dụng computer vision và để tăng tốc việc sử dụng machine perception trong các sản phẩm thương mại.",
        shortDescription: "Thư viện Computer Vision mã nguồn mở hàng đầu",
        githubUrl: "https://github.com/opencv/opencv",
        githubOwner: "opencv",
        githubRepo: "opencv",
        githubStars: 78000,
        githubForks: 55000,
        githubIssues: 3500,
        websiteUrl: "https://opencv.org",
        documentationUrl: "https://docs.opencv.org",
        categoryId: insertedCategories.find(c => c.slug === "computer-vision")!.id,
        tags: ["computer-vision", "image-processing", "video-analysis", "c++", "python"],
        primaryLanguage: "C++",
        languages: ["C++", "Python", "Java"],
        license: "Apache-2.0",
        status: "Published",
        isFeatured: true,
        isOpenSource: true,
        viewCount: 12800,
        clickCount: 2890,
      },
      {
        name: "Hugging Face Transformers",
        slug: "huggingface-transformers",
        description: "🤗 Transformers cung cấp hàng nghìn pre-trained models để thực hiện các tác vụ trên các modality khác nhau như text, vision và audio. Những models này có thể được áp dụng cho Text classification, Information extraction, Question answering, Summarization, Translation, Text generation trong hơn 100 ngôn ngữ.",
        shortDescription: "State-of-the-art Machine Learning cho PyTorch, TensorFlow và JAX",
        githubUrl: "https://github.com/huggingface/transformers",
        githubOwner: "huggingface",
        githubRepo: "transformers",
        githubStars: 132000,
        githubForks: 26000,
        githubIssues: 900,
        websiteUrl: "https://huggingface.co/transformers",
        documentationUrl: "https://huggingface.co/docs/transformers",
        categoryId: insertedCategories.find(c => c.slug === "nlp")!.id,
        tags: ["nlp", "transformers", "bert", "gpt", "pytorch", "tensorflow"],
        primaryLanguage: "Python",
        languages: ["Python"],
        license: "Apache-2.0",
        status: "Published",
        isFeatured: true,
        isOpenSource: true,
        viewCount: 18900,
        clickCount: 4120,
      },
      {
        name: "Pandas",
        slug: "pandas",
        description: "pandas là một thư viện Python mã nguồn mở, được cấp phép BSD cung cấp các cấu trúc dữ liệu hiệu suất cao, dễ sử dụng và các công cụ phân tích dữ liệu cho ngôn ngữ lập trình Python. pandas là một NumFOCUS sponsored project.",
        shortDescription: "Powerful data structures for data analysis, time series, và statistics",
        githubUrl: "https://github.com/pandas-dev/pandas",
        githubOwner: "pandas-dev",
        githubRepo: "pandas",
        githubStars: 43000,
        githubForks: 17800,
        githubIssues: 3600,
        websiteUrl: "https://pandas.pydata.org",
        documentationUrl: "https://pandas.pydata.org/docs",
        categoryId: insertedCategories.find(c => c.slug === "data-science")!.id,
        tags: ["data-analysis", "dataframes", "statistics", "python", "csv", "excel"],
        primaryLanguage: "Python",
        languages: ["Python", "C"],
        license: "BSD-3-Clause",
        status: "Published",
        isFeatured: true,
        isOpenSource: true,
        viewCount: 9800,
        clickCount: 2100,
      },
      {
        name: "ROS (Robot Operating System)",
        slug: "ros",
        description: "ROS là một tập hợp các thư viện phần mềm và công cụ giúp bạn xây dựng các ứng dụng robot. Từ drivers đến state-of-the-art algorithms, và với powerful developer tools, ROS có những gì bạn cần cho dự án robotics tiếp theo của mình.",
        shortDescription: "Flexible framework cho việc viết phần mềm robot",
        githubUrl: "https://github.com/ros/ros",
        githubOwner: "ros",
        githubRepo: "ros",
        githubStars: 3600,
        githubForks: 1200,
        githubIssues: 180,
        websiteUrl: "https://ros.org",
        documentationUrl: "https://wiki.ros.org",
        categoryId: insertedCategories.find(c => c.slug === "robotics")!.id,
        tags: ["robotics", "middleware", "simulation", "sensors", "actuators"],
        primaryLanguage: "C++",
        languages: ["C++", "Python"],
        license: "BSD-3-Clause",
        status: "Published",
        isFeatured: false,
        isOpenSource: true,
        viewCount: 5600,
        clickCount: 890,
      },
      {
        name: "PyTorch",
        slug: "pytorch",
        description: "PyTorch là một thư viện machine learning mã nguồn mở dựa trên thư viện Torch, được sử dụng cho các ứng dụng như computer vision và natural language processing, chủ yếu được phát triển bởi Meta AI.",
        shortDescription: "Tensors và Dynamic neural networks trong Python với strong GPU acceleration",
        githubUrl: "https://github.com/pytorch/pytorch",
        githubOwner: "pytorch",
        githubRepo: "pytorch",
        githubStars: 82000,
        githubForks: 22000,
        githubIssues: 13000,
        websiteUrl: "https://pytorch.org",
        documentationUrl: "https://pytorch.org/docs",
        categoryId: insertedCategories.find(c => c.slug === "deep-learning")!.id,
        tags: ["deep-learning", "neural-networks", "gpu", "python", "facebook"],
        primaryLanguage: "Python",
        languages: ["Python", "C++", "C"],
        license: "BSD-3-Clause",
        status: "Published",
        isFeatured: true,
        isOpenSource: true,
        viewCount: 21500,
        clickCount: 5200,
      },
    ];

    const insertedProjects = await db.insert(projects).values(projectData).returning();
    console.log(`✅ Đã tạo ${insertedProjects.length} projects`);

    console.log("🎉 Seed database hoàn thành!");
    console.log(`📊 Tổng kết:`);
    console.log(`   - ${insertedCategories.length} categories`);
    console.log(`   - ${insertedProjects.length} projects`);

  } catch (error) {
    console.error("❌ Lỗi khi seed database:", error);
    throw error;
  }
}

// Chạy seed function
if (require.main === module) {
  seed()
    .then(() => {
      console.log("✅ Seed hoàn thành thành công!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Seed thất bại:", error);
      process.exit(1);
    });
}

export { seed };