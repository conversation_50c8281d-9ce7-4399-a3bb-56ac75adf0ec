import { Suspense } from "react";
import { <PERSON>adata } from "next";
import { Shell } from "@/components/shell";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { getAllPaginatedSubmissionsQuery } from "@/server/actions/submissions/queries";
import { PendingSubmissionsTable } from "./_components/pending-submissions-table";
import { SearchParams } from "@/types/data-table";
import { z } from "zod";
import { Badge } from "@/components/ui/badge";
import { Clock } from "lucide-react";

/**
 * Schema validation cho search parameters của trang pending submissions
 */
const searchParamsSchema = z.object({
    page: z.coerce.number().default(1),
    per_page: z.coerce.number().default(10),
    sort: z.string().optional(),
    submitter: z.string().optional(),
    from: z.string().optional(),
    to: z.string().optional(),
});

export const metadata: Metadata = {
    title: "Pending Submissions - Admin",
    description: "Manage pending project submissions awaiting review",
};

interface PendingSubmissionsPageProps {
    searchParams: SearchParams;
}

/**
 * Trang quản lý submissions đang chờ xử lý (Pending)
 * Hiển thị danh sách các submissions có status "Pending" để admin dễ dàng xem xét và xử lý
 */
export default function PendingSubmissionsPage({ searchParams }: PendingSubmissionsPageProps) {
    const search = searchParamsSchema.parse(searchParams);

    // Lấy danh sách submissions với filter status = "Pending"
    const submissionsPromise = getAllPaginatedSubmissionsQuery({
        page: search.page,
        per_page: search.per_page,
        sort: search.sort,
        status: "Pending", // Chỉ lấy submissions pending
        submitter: search.submitter,
        from: search.from,
        to: search.to,
    });

    return (
        <Shell variant="sidebar">
            <div className="space-y-6">
                {/* Page Header */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div className="space-y-1">
                        <div className="flex items-center gap-2">
                            <h1 className="text-2xl font-semibold tracking-tight">
                                Pending Submissions
                            </h1>
                            <Badge variant="secondary" className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                Awaiting Review
                            </Badge>
                        </div>
                        <p className="text-muted-foreground">
                            Review and manage project submissions that are pending approval. 
                            Take action to approve, reject, or request changes.
                        </p>
                    </div>
                </div>

                {/* Submissions Table */}
                <Suspense
                    fallback={
                        <DataTableSkeleton
                            columnCount={7}
                            searchableColumnCount={2}
                            filterableColumnCount={1}
                            cellWidths={["10rem", "20rem", "15rem", "10rem", "12rem", "10rem", "8rem"]}
                            shrinkZero
                        />
                    }
                >
                    <PendingSubmissionsTable submissionsPromise={submissionsPromise} />
                </Suspense>
            </div>
        </Shell>
    );
}