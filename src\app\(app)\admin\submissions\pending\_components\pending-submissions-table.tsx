"use client";

import { DataTable } from "@/components/data-table/data-table";
import { DataTableSearchableColumn } from "@/types/data-table";
import { columns, type Submission } from "../../_components/columns";
import { use } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface PendingSubmissionsTableProps {
    submissionsPromise: Promise<{
        data: Submission[];
        pageCount: number;
    }>;
}

/**
 * Searchable columns configuration cho pending submissions table
 */
const searchableColumns: DataTableSearchableColumn<Submission>[] = [
    {
        id: "projectData.name",
        placeholder: "Filter by project name...",
    },
    {
        id: "submitter",
        placeholder: "Filter by submitter...",
    },
];

/**
 * Quick action buttons cho pending submissions
 */
const quickActions = [
    {
        label: "Approve All",
        icon: CheckCircle,
        variant: "default" as const,
        action: "approve",
    },
    {
        label: "Review Selected",
        icon: AlertCircle,
        variant: "secondary" as const,
        action: "review",
    },
];

/**
 * Component hiển thị bảng submissions pending với các tính năng tối ưu cho việc xử lý
 * Bao gồm thống kê nhanh và các quick actions
 */
export function PendingSubmissionsTable({ submissionsPromise }: PendingSubmissionsTableProps) {
    const { data, pageCount } = use(submissionsPromise);
    
    // Tính toán thống kê
    const totalPending = data.length;
    const oldestSubmission = data.length > 0 ? 
        Math.max(...data.map(s => new Date().getTime() - new Date(s.submittedAt).getTime())) / (1000 * 60 * 60 * 24) : 0;
    const todaySubmissions = data.filter(s => {
        const today = new Date();
        const submissionDate = new Date(s.submittedAt);
        return submissionDate.toDateString() === today.toDateString();
    }).length;

    return (
        <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Pending</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{totalPending}</div>
                        <p className="text-xs text-muted-foreground">
                            Submissions awaiting review
                        </p>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Today's Submissions</CardTitle>
                        <Badge variant="secondary">{todaySubmissions}</Badge>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{todaySubmissions}</div>
                        <p className="text-xs text-muted-foreground">
                            New submissions today
                        </p>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Oldest Pending</CardTitle>
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{Math.floor(oldestSubmission)}</div>
                        <p className="text-xs text-muted-foreground">
                            Days since oldest submission
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Quick Actions */}
            {totalPending > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle className="text-lg">Quick Actions</CardTitle>
                        <CardDescription>
                            Perform bulk actions on pending submissions
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-2">
                            {quickActions.map((action) => {
                                const Icon = action.icon;
                                return (
                                    <Button
                                        key={action.action}
                                        variant={action.variant}
                                        size="sm"
                                        className="flex items-center gap-2"
                                        onClick={() => {
                                            // TODO: Implement bulk actions
                                            console.log(`${action.action} action triggered`);
                                        }}
                                    >
                                        <Icon className="h-4 w-4" />
                                        {action.label}
                                    </Button>
                                );
                            })}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Data Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Pending Submissions</CardTitle>
                    <CardDescription>
                        Review and take action on submissions awaiting approval
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <DataTable
                        columns={columns}
                        data={data}
                        pageCount={pageCount}
                        searchableColumns={searchableColumns}
                        newRowLink="/submit"
                        deleteRowsAction={async (rows) => {
                            // TODO: Implement bulk delete if needed
                            console.log("Delete rows:", rows);
                        }}
                    />
                </CardContent>
            </Card>
        </div>
    );
}