{"name": "launchmvpfast-saas", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "db:push": "drizzle-kit push:pg", "db:seed": "bun run src/server/db/seed.ts", "db:studio": "drizzle-kit studio --config=drizzle.config.ts", "dev": "next dev", "dev:turbo": "next dev --turbo", "lint": "next lint", "start": "next start"}, "dependencies": {"@auth/drizzle-adapter": "^0.7.0", "@hookform/resolvers": "^3.3.4", "@lemonsqueezy/lemonsqueezy.js": "^2.2.0", "@mdx-js/react": "^3.0.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@t3-oss/env-nextjs": "^0.9.2", "@tanstack/react-query": "^5.29.2", "@tanstack/react-table": "^8.16.0", "@types/mdx": "^2.0.13", "@uploadthing/react": "^6.4.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.29.4", "drizzle-zod": "^0.5.1", "fumadocs-core": "^11.1.1", "fumadocs-mdx": "^8.2.19", "fumadocs-ui": "^11.1.1", "geist": "^1.3.0", "json2csv": "6.0.0-alpha.2", "lucide-react": "^0.368.0", "next": "^14.2.1", "next-auth": "^4.24.6", "next-themes": "^0.3.0", "nodemailer": "^6.9.13", "postgres": "^3.4.3", "posthog-js": "^1.130.0", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.51.3", "react-wrap-balancer": "^1.1.0", "recharts": "^2.12.6", "resend": "^3.2.0", "server-only": "^0.0.1", "sonner": "^1.4.41", "superjson": "^2.2.1", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.9.0", "uuid": "^9.0.1", "vaul": "^0.9.0", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.12", "@types/eslint": "^8.56.9", "@types/json2csv": "^5.0.7", "@types/node": "^20.12.7", "@types/react": "^18.2.60", "@types/react-dom": "^18.2.19", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "autoprefixer": "^10.4.19", "drizzle-kit": "^0.20.17", "eslint": "^8.57.0", "eslint-config-next": "^14.2.1", "pg": "^8.11.3", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.3", "typescript": "^5.4.5"}, "ct3aMetadata": {"initVersion": "7.26.0"}, "packageManager": "pnpm@8.10.0"}