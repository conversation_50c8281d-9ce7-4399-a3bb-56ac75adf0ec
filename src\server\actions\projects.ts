"use server";

import { db } from "@/server/db";
import { projects, categories } from "@/server/db/schema";
import { eq, desc, and, ilike } from "drizzle-orm";
import { unstable_cache } from "next/cache";

/**
 * <PERSON><PERSON>y danh sách tất cả projects với thông tin category
 * Kết quả được cache để tối ưu performance
 */
export const getProjects = unstable_cache(
    async () => {
        try {
            const result = await db
                .select({
                    id: projects.id,
                    name: projects.name,
                    slug: projects.slug,
                    description: projects.description,
                    shortDescription: projects.shortDescription,
                    websiteUrl: projects.websiteUrl,
                    githubUrl: projects.githubUrl,
                    githubStars: projects.githubStars,
                    logo: projects.logo,
                    tags: projects.tags,
                    isFeatured: projects.isFeatured,
                    status: projects.status,
                    viewCount: projects.viewCount,
                    createdAt: projects.createdAt,
                    category: categories.name,
                    categorySlug: categories.slug,
                })
                .from(projects)
                .leftJoin(categories, eq(projects.categoryId, categories.id))
                .where(eq(projects.status, "Published"))
                .orderBy(desc(projects.isFeatured), desc(projects.githubStars), desc(projects.createdAt));

            return result;
        } catch (error) {
            console.error("Error fetching projects:", error);
            return [];
        }
    },
    ["projects"],
    {
        revalidate: 3600, // Cache for 1 hour
        tags: ["projects"],
    }
);

/**
 * Lấy danh sách projects featured (nổi bật)
 */
export const getFeaturedProjects = unstable_cache(
    async (limit: number = 8) => {
        try {
            const result = await db
                .select({
                    id: projects.id,
                    name: projects.name,
                    slug: projects.slug,
                    description: projects.description,
                    shortDescription: projects.shortDescription,
                    websiteUrl: projects.websiteUrl,
                    githubUrl: projects.githubUrl,
                    githubStars: projects.githubStars,
                    logo: projects.logo,
                    tags: projects.tags,
                    isFeatured: projects.isFeatured,
                    status: projects.status,
                    viewCount: projects.viewCount,
                    createdAt: projects.createdAt,
                    category: categories.name,
                    categorySlug: categories.slug,
                })
                .from(projects)
                .leftJoin(categories, eq(projects.categoryId, categories.id))
                .where(and(eq(projects.status, "Published"), eq(projects.isFeatured, true)))
                .orderBy(desc(projects.githubStars), desc(projects.createdAt))
                .limit(limit);

            return result;
        } catch (error) {
            console.error("Error fetching featured projects:", error);
            return [];
        }
    },
    ["featured-projects"],
    {
        revalidate: 3600,
        tags: ["projects", "featured-projects"],
    }
);

/**
 * Lấy projects theo category name
 */
export const getProjectsByCategory = unstable_cache(
    async (categoryName: string, options?: { search?: string; sort?: string; tags?: string[]; limit?: number }) => {
        try {
            let query = db
                .select({
                    id: projects.id,
                    name: projects.name,
                    slug: projects.slug,
                    description: projects.description,
                    shortDescription: projects.shortDescription,
                    websiteUrl: projects.websiteUrl,
                    githubUrl: projects.githubUrl,
                    githubStars: projects.githubStars,
                    logo: projects.logo,
                    tags: projects.tags,
                    isFeatured: projects.isFeatured,
                    status: projects.status,
                    viewCount: projects.viewCount,
                    createdAt: projects.createdAt,
                    category: categories.name,
                    categorySlug: categories.slug,
                })
                .from(projects)
                .leftJoin(categories, eq(projects.categoryId, categories.id))
                .where(and(
                    eq(projects.status, "Published"),
                    ilike(categories.name, `%${categoryName}%`)
                ))
                .orderBy(desc(projects.isFeatured), desc(projects.githubStars), desc(projects.createdAt));

            // Apply search filter if provided
            if (options?.search) {
                query = query.where(and(
                    eq(projects.status, "Published"),
                    ilike(categories.name, `%${categoryName}%`),
                    ilike(projects.name, `%${options.search}%`)
                ));
            }

            if (options?.limit) {
                query = query.limit(options.limit);
            }

            const result = await query;
            return result;
        } catch (error) {
            console.error("Error fetching projects by category:", error);
            return [];
        }
    },
    ["projects-by-category"],
    {
        revalidate: 3600,
        tags: ["projects"],
    }
);

/**
 * Tìm kiếm projects theo từ khóa
 */
export const searchProjects = async (query: string) => {
    try {
        const result = await db
            .select({
                id: projects.id,
                name: projects.name,
                slug: projects.slug,
                description: projects.description,
                shortDescription: projects.shortDescription,
                websiteUrl: projects.websiteUrl,
                githubUrl: projects.githubUrl,
                githubStars: projects.githubStars,
                logo: projects.logo,
                tags: projects.tags,
                isFeatured: projects.isFeatured,
                status: projects.status,
                viewCount: projects.viewCount,
                createdAt: projects.createdAt,
                category: categories.name,
                categorySlug: categories.slug,
            })
            .from(projects)
            .leftJoin(categories, eq(projects.categoryId, categories.id))
            .where(and(
                eq(projects.status, "Published"),
                ilike(projects.name, `%${query}%`)
            ))
            .orderBy(desc(projects.isFeatured), desc(projects.githubStars), desc(projects.createdAt))
            .limit(20);

        return result;
    } catch (error) {
        console.error("Error searching projects:", error);
        return [];
    }
};

/**
 * Lấy danh sách categories
 */
export const getCategories = unstable_cache(
    async () => {
        try {
            const result = await db
                .select()
                .from(categories)
                .orderBy(categories.name);

            return result;
        } catch (error) {
            console.error("Error fetching categories:", error);
            return [];
        }
    },
    ["categories"],
    {
        revalidate: 3600,
        tags: ["categories"],
    }
);

/**
 * Lấy thông tin chi tiết của một project theo ID
 */
export const getProjectById = unstable_cache(
    async (id: string) => {
        try {
            const result = await db
                .select({
                    id: projects.id,
                    name: projects.name,
                    slug: projects.slug,
                    description: projects.description,
                    shortDescription: projects.shortDescription,
                    websiteUrl: projects.websiteUrl,
                    demoUrl: projects.demoUrl,
                    documentationUrl: projects.documentationUrl,
                    githubUrl: projects.githubUrl,
                    githubOwner: projects.githubOwner,
                    githubRepo: projects.githubRepo,
                    githubStars: projects.githubStars,
                    githubForks: projects.githubForks,
                    githubIssues: projects.githubIssues,
                    logo: projects.logo,
                    screenshots: projects.screenshots,
                    tags: projects.tags,
                    primaryLanguage: projects.primaryLanguage,
                    languages: projects.languages,
                    license: projects.license,
                    viewCount: projects.viewCount,
                    clickCount: projects.clickCount,
                    status: projects.status,
                    isFeatured: projects.isFeatured,
                    isOpenSource: projects.isOpenSource,
                    createdAt: projects.createdAt,
                    updatedAt: projects.updatedAt,
                    category: categories.name,
                    categoryId: projects.categoryId,
                    categorySlug: categories.slug,
                })
                .from(projects)
                .leftJoin(categories, eq(projects.categoryId, categories.id))
                .where(and(eq(projects.id, id), eq(projects.status, "Published")))
                .limit(1);

            return result[0] || null;
        } catch (error) {
            console.error("Error fetching project by ID:", error);
            return null;
        }
    },
    ["project-by-id"],
    {
        revalidate: 3600,
        tags: ["projects"],
    }
);

// Type definitions for the returned data
export type ProjectWithCategory = Awaited<ReturnType<typeof getProjects>>[0];
export type CategoryType = Awaited<ReturnType<typeof getCategories>>[0];
export type ProjectDetail = Awaited<ReturnType<typeof getProjectById>>;