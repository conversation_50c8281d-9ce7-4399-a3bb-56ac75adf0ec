# Project Card Redesign - UI Components

## Tổng Quan

Đã thiết kế lại hoàn toàn Project Card component theo phong cách OpenAlternative/Discourse với các cải tiến đáng kể về UI/UX và tính năng.

## 🎨 Các Component Mới

### 1. ProjectCard (Redesigned)
**File:** `src/app/(web)/_components/project-card.tsx`

**Tính năng:**
- ✅ Logo/Avatar với fallback gradient đẹp
- ✅ GitHub statistics (stars, forks, issues, last commit)
- ✅ Alternative information ("Alternative to X")
- ✅ Category colors
- ✅ 2 variants: default (grid) và compact (list)
- ✅ Responsive design
- ✅ Hover effects và animations

**Props:**
```typescript
interface ProjectCardProps {
    project: ProjectWithCategory;
    variant?: "default" | "compact";
}
```

### 2. ProjectLogo
**File:** `src/app/(web)/_components/project-logo.tsx`

**Tính năng:**
- ✅ Hiển thị logo project hoặc GitHub avatar
- ✅ Fallback gradient với initials
- ✅ Multiple sizes: sm, md, lg, xl
- ✅ Multiple rounded corners: sm, md, lg, xl, full

**Props:**
```typescript
interface ProjectLogoProps {
    project: {
        name: string;
        logo?: string | null;
        githubUrl?: string | null;
    };
    size?: "sm" | "md" | "lg" | "xl";
    className?: string;
    rounded?: "sm" | "md" | "lg" | "xl" | "full";
}
```

### 3. GitHubStats
**File:** `src/app/(web)/_components/github-stats.tsx`

**Tính năng:**
- ✅ Format số đẹp (1.2K, 1.5M)
- ✅ 3 variants: compact, default, detailed
- ✅ Hiển thị stars, forks, issues, last commit
- ✅ Responsive labels

**Props:**
```typescript
interface GitHubStatsProps {
    stars?: number | null;
    forks?: number | null;
    issues?: number | null;
    lastCommit?: Date | null;
    className?: string;
    variant?: "default" | "compact" | "detailed";
    showLabels?: boolean;
}
```

### 4. AlternativeBadge
**File:** `src/app/(web)/_components/alternative-badge.tsx`

**Tính năng:**
- ✅ Hiển thị thông tin "Alternative to"
- ✅ Extract alternatives từ tags và description
- ✅ Hardcoded mapping cho projects phổ biến
- ✅ 2 variants: default và compact

**Props:**
```typescript
interface AlternativeBadgeProps {
    alternatives: string[];
    className?: string;
    variant?: "default" | "compact";
}
```

### 5. ProjectsGrid (Updated)
**File:** `src/app/(web)/_components/projects-grid.tsx`

**Tính năng:**
- ✅ Hỗ trợ variant cho ProjectCard
- ✅ Flexible columns (auto, 1-5)
- ✅ Compact layout cho list view

**Props:**
```typescript
interface ProjectsGridProps {
    projects: ProjectWithCategory[];
    title?: string;
    description?: string;
    variant?: "default" | "compact";
    columns?: "auto" | 1 | 2 | 3 | 4 | 5;
}
```

## 🛠️ Utility Functions

### Avatar Utils
**File:** `src/lib/avatar-utils.ts`

**Functions:**
- `getProjectInitials(name: string)` - Tạo initials từ tên
- `getProjectGradient(name: string)` - Tạo gradient color
- `getDiceBearAvatar(name: string)` - Tạo DiceBear avatar URL
- `getGitHubAvatar(githubUrl?: string)` - Lấy GitHub avatar
- `getProjectAvatar(project)` - Tổng hợp avatar info
- `getCategoryColor(categoryName: string)` - Tạo category color

## 📱 Demo Pages

### 1. Component Demo
**URL:** `/demo`
**File:** `src/app/(web)/demo/page.tsx`

Hiển thị tất cả variants của ProjectCard với mock data.

### 2. UI Comparison
**URL:** `/ui-comparison`
**File:** `src/app/(web)/ui-comparison/page.tsx`

So sánh thiết kế trước và sau khi cải thiện.

## 🎯 Cách Sử dụng

### Basic Usage
```tsx
import { ProjectCard } from "@/app/(web)/_components/project-card";

// Default grid card
<ProjectCard project={project} variant="default" />

// Compact list card
<ProjectCard project={project} variant="compact" />
```

### Grid Layout
```tsx
import { ProjectsGrid } from "@/app/(web)/_components/projects-grid";

// Default grid
<ProjectsGrid projects={projects} />

// Compact list
<ProjectsGrid projects={projects} variant="compact" />

// Custom columns
<ProjectsGrid projects={projects} columns={3} />
```

### Individual Components
```tsx
import { ProjectLogo } from "@/app/(web)/_components/project-logo";
import { GitHubStats } from "@/app/(web)/_components/github-stats";
import { AlternativeBadge } from "@/app/(web)/_components/alternative-badge";

<ProjectLogo project={project} size="lg" rounded="xl" />

<GitHubStats 
    stars={project.githubStars}
    forks={project.githubForks}
    variant="detailed"
/>

<AlternativeBadge 
    alternatives={["Discord", "Slack"]} 
    variant="compact" 
/>
```

## 🎨 Design System

### Colors
- **Primary:** Sử dụng cho links, highlights
- **Muted:** Cho secondary text
- **Category Colors:** Auto-generated từ tên category
- **Gradient Avatars:** Auto-generated từ tên project

### Typography
- **Project Name:** font-semibold text-lg (default) / text-base (compact)
- **Description:** text-sm text-muted-foreground
- **Stats:** text-xs (compact) / text-sm (default)

### Spacing
- **Card Padding:** p-6 (default) / p-4 (compact)
- **Gap:** gap-3 (compact) / gap-4 (default)
- **Grid Gap:** gap-6

## 🚀 Performance

- ✅ Lazy loading cho images
- ✅ Optimized re-renders với React.memo
- ✅ Efficient date formatting
- ✅ Minimal bundle size với tree-shaking

## 📋 TODO

- [ ] Add animation cho stats counter
- [ ] Implement skeleton loading states
- [ ] Add more alternative mappings
- [ ] Support cho custom themes
- [ ] Add accessibility improvements
- [ ] Implement virtual scrolling cho large lists

## 🔧 Dependencies

```json
{
    "date-fns": "^4.1.0",
    "lucide-react": "^0.368.0",
    "@radix-ui/react-avatar": "^1.0.4",
    "@radix-ui/react-separator": "^1.0.3"
}
```

## 📸 Screenshots

Xem demo tại:
- http://localhost:3001/demo
- http://localhost:3001/ui-comparison
