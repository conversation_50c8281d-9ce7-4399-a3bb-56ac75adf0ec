"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
    CheckCircle, 
    XCircle, 
    Clock, 
    Eye, 
    ArrowLeft, 
    ExternalLink,
    Calendar,
    User,
    Mail,
    Tag,
    FileText
} from "lucide-react";
import { format } from "date-fns";
import Link from "next/link";
import { siteUrls } from "@/config/urls";
import { toast } from "sonner";
import {
    approveSubmissionAction,
    rejectSubmissionAction,
    requestChangesAction,
    markUnderReviewAction,
} from "@/server/actions/submissions/mutations";

type SubmissionWithDetails = {
    id: string;
    projectData: any;
    submittedBy: string;
    submitterEmail: string;
    submitterName: string | null;
    status: "Pending" | "Under Review" | "Approved" | "Rejected" | "Needs Changes";
    reviewNotes: string | null;
    reviewedBy: string | null;
    reviewedAt: Date | null;
    projectId: string | null;
    createdAt: Date;
    updatedAt: Date;
    submitter: {
        id: string;
        name: string | null;
        email: string;
        image: string | null;
    } | null;
};

interface SubmissionDetailsProps {
    submission: SubmissionWithDetails;
}

/**
 * Get status badge with appropriate styling
 */
function getStatusBadge(status: SubmissionWithDetails["status"]) {
    switch (status) {
        case "Pending":
            return <Badge variant="secondary"><Clock className="mr-1 h-3 w-3" />Pending</Badge>;
        case "Under Review":
            return <Badge variant="default"><Eye className="mr-1 h-3 w-3" />Under Review</Badge>;
        case "Approved":
            return <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200"><CheckCircle className="mr-1 h-3 w-3" />Approved</Badge>;
        case "Rejected":
            return <Badge variant="destructive"><XCircle className="mr-1 h-3 w-3" />Rejected</Badge>;
        case "Needs Changes":
            return <Badge variant="outline"><Clock className="mr-1 h-3 w-3" />Needs Changes</Badge>;
        default:
            return <Badge variant="secondary">{status}</Badge>;
    }
}

export function SubmissionDetails({ submission }: SubmissionDetailsProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [reviewNotes, setReviewNotes] = useState(submission.reviewNotes || "");
    
    const projectData = submission.projectData as any;
    const canTakeAction = ["Pending", "Under Review", "Needs Changes"].includes(submission.status);

    /**
     * Handle submission approval
     */
    const handleApprove = async () => {
        setIsLoading(true);
        try {
            await approveSubmissionAction({
                submissionId: submission.id,
                reviewNotes: reviewNotes.trim() || undefined,
            });
            toast.success("Submission approved successfully!");
            // Refresh the page to show updated status
            window.location.reload();
        } catch (error) {
            toast.error("Failed to approve submission");
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * Handle submission rejection
     */
    const handleReject = async () => {
        if (!reviewNotes.trim()) {
            toast.error("Please provide review notes for rejection");
            return;
        }
        
        setIsLoading(true);
        try {
            await rejectSubmissionAction({
                submissionId: submission.id,
                reviewNotes: reviewNotes.trim(),
            });
            toast.success("Submission rejected");
            window.location.reload();
        } catch (error) {
            toast.error("Failed to reject submission");
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * Handle request for changes
     */
    const handleRequestChanges = async () => {
        if (!reviewNotes.trim()) {
            toast.error("Please provide review notes for requested changes");
            return;
        }
        
        setIsLoading(true);
        try {
            await requestChangesAction({
                submissionId: submission.id,
                reviewNotes: reviewNotes.trim(),
            });
            toast.success("Changes requested");
            window.location.reload();
        } catch (error) {
            toast.error("Failed to request changes");
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * Mark submission as under review
     */
    const handleMarkUnderReview = async () => {
        setIsLoading(true);
        try {
            await markUnderReviewAction({
                submissionId: submission.id,
            });
            toast.success("Marked as under review");
            window.location.reload();
        } catch (error) {
            toast.error("Failed to update status");
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div className="space-y-1">
                    <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm" asChild>
                            <Link href={siteUrls.admin.submissions}>
                                <ArrowLeft className="mr-1 h-4 w-4" />
                                Back to Submissions
                            </Link>
                        </Button>
                    </div>
                    <h1 className="text-2xl font-semibold tracking-tight">
                        {projectData?.name || "Untitled Project"}
                    </h1>
                    <div className="flex items-center gap-2">
                        {getStatusBadge(submission.status)}
                        <span className="text-sm text-muted-foreground">
                            Submitted {format(submission.createdAt, "MMM dd, yyyy 'at' HH:mm")}
                        </span>
                    </div>
                </div>
                
                {canTakeAction && (
                    <div className="flex gap-2">
                        {submission.status === "Pending" && (
                            <Button
                                variant="outline"
                                onClick={handleMarkUnderReview}
                                disabled={isLoading}
                            >
                                <Eye className="mr-1 h-4 w-4" />
                                Mark Under Review
                            </Button>
                        )}
                        <Button
                            variant="outline"
                            onClick={handleRequestChanges}
                            disabled={isLoading}
                        >
                            <Clock className="mr-1 h-4 w-4" />
                            Request Changes
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleReject}
                            disabled={isLoading}
                        >
                            <XCircle className="mr-1 h-4 w-4" />
                            Reject
                        </Button>
                        <Button
                            onClick={handleApprove}
                            disabled={isLoading}
                        >
                            <CheckCircle className="mr-1 h-4 w-4" />
                            Approve
                        </Button>
                    </div>
                )}
            </div>

            <div className="grid gap-6 lg:grid-cols-3">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Project Details */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                Project Details
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <Label className="text-sm font-medium">Project Name</Label>
                                    <p className="text-sm text-muted-foreground mt-1">
                                        {projectData?.name || "N/A"}
                                    </p>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium">Category</Label>
                                    <p className="text-sm text-muted-foreground mt-1">
                                        <Badge variant="outline">
                                            {projectData?.category || "Uncategorized"}
                                        </Badge>
                                    </p>
                                </div>
                            </div>
                            
                            {projectData?.description && (
                                <div>
                                    <Label className="text-sm font-medium">Description</Label>
                                    <p className="text-sm text-muted-foreground mt-1 whitespace-pre-wrap">
                                        {projectData.description}
                                    </p>
                                </div>
                            )}
                            
                            {projectData?.website && (
                                <div>
                                    <Label className="text-sm font-medium">Website</Label>
                                    <p className="text-sm text-muted-foreground mt-1">
                                        <Link 
                                            href={projectData.website} 
                                            target="_blank" 
                                            className="inline-flex items-center gap-1 text-blue-600 hover:underline"
                                        >
                                            {projectData.website}
                                            <ExternalLink className="h-3 w-3" />
                                        </Link>
                                    </p>
                                </div>
                            )}
                            
                            {projectData?.tags && projectData.tags.length > 0 && (
                                <div>
                                    <Label className="text-sm font-medium">Tags</Label>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                        {projectData.tags.map((tag: string, index: number) => (
                                            <Badge key={index} variant="secondary" className="text-xs">
                                                {tag}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Review Notes */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Review Notes</CardTitle>
                            <CardDescription>
                                Add notes for your review decision
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Textarea
                                placeholder="Enter your review notes here..."
                                value={reviewNotes}
                                onChange={(e) => setReviewNotes(e.target.value)}
                                rows={4}
                                disabled={!canTakeAction}
                            />
                        </CardContent>
                    </Card>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                    {/* Submitter Info */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Submitter
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-3">
                                <Avatar>
                                    <AvatarImage src={submission.submitter?.image || undefined} />
                                    <AvatarFallback>
                                        {(submission.submitter?.name || submission.submitterName || "U").charAt(0).toUpperCase()}
                                    </AvatarFallback>
                                </Avatar>
                                <div>
                                    <p className="font-medium">
                                        {submission.submitter?.name || submission.submitterName || "Unknown"}
                                    </p>
                                    <p className="text-sm text-muted-foreground flex items-center gap-1">
                                        <Mail className="h-3 w-3" />
                                        {submission.submitter?.email || submission.submitterEmail}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Submission Info */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Submission Info
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div>
                                <Label className="text-sm font-medium">Submitted</Label>
                                <p className="text-sm text-muted-foreground">
                                    {format(submission.createdAt, "MMM dd, yyyy 'at' HH:mm")}
                                </p>
                            </div>
                            
                            {submission.reviewedAt && (
                                <div>
                                    <Label className="text-sm font-medium">Last Reviewed</Label>
                                    <p className="text-sm text-muted-foreground">
                                        {format(submission.reviewedAt, "MMM dd, yyyy 'at' HH:mm")}
                                    </p>
                                </div>
                            )}
                            
                            <div>
                                <Label className="text-sm font-medium">Submission ID</Label>
                                <p className="text-sm text-muted-foreground font-mono">
                                    {submission.id}
                                </p>
                            </div>
                            
                            {submission.projectId && (
                                <div>
                                    <Label className="text-sm font-medium">Project ID</Label>
                                    <p className="text-sm text-muted-foreground font-mono">
                                        {submission.projectId}
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Previous Review Notes */}
                    {submission.reviewNotes && submission.status !== "Pending" && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Previous Review</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm whitespace-pre-wrap">
                                    {submission.reviewNotes}
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </div>
    );
}