"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { type ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, Eye, CheckCircle, XCircle, Clock } from "lucide-react";
import { format } from "date-fns";
import Link from "next/link";
import { siteUrls } from "@/config/urls";

// Type definition for submission data
export type Submission = {
    id: string;
    projectData: any;
    submittedBy: string;
    submitterEmail: string;
    submitterName: string | null;
    status: "Pending" | "Under Review" | "Approved" | "Rejected" | "Needs Changes";
    reviewNotes: string | null;
    reviewedBy: string | null;
    reviewedAt: Date | null;
    projectId: string | null;
    createdAt: Date;
    updatedAt: Date;
    submitter: {
        id: string;
        name: string | null;
        email: string;
        image: string | null;
    } | null;
};

/**
 * Get status badge variant based on submission status
 */
function getStatusBadge(status: Submission["status"]) {
    switch (status) {
        case "Pending":
            return <Badge variant="secondary"><Clock className="mr-1 h-3 w-3" />Pending</Badge>;
        case "Under Review":
            return <Badge variant="default"><Eye className="mr-1 h-3 w-3" />Under Review</Badge>;
        case "Approved":
            return <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-200"><CheckCircle className="mr-1 h-3 w-3" />Approved</Badge>;
        case "Rejected":
            return <Badge variant="destructive"><XCircle className="mr-1 h-3 w-3" />Rejected</Badge>;
        case "Needs Changes":
            return <Badge variant="outline"><Clock className="mr-1 h-3 w-3" />Needs Changes</Badge>;
        default:
            return <Badge variant="secondary">{status}</Badge>;
    }
}

export const columns: ColumnDef<Submission>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
            />
        ),
        enableSorting: false,
        enableHiding: false,
    },
    {
        accessorKey: "projectData.name",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Project Name" />
        ),
        cell: ({ row }) => {
            const projectData = row.original.projectData as any;
            return (
                <div className="font-medium">
                    {projectData?.name || "Untitled Project"}
                </div>
            );
        },
    },
    {
        accessorKey: "submitter",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Submitter" />
        ),
        cell: ({ row }) => {
            const submitter = row.original.submitter;
            const submitterName = row.original.submitterName;
            const submitterEmail = row.original.submitterEmail;
            
            return (
                <div className="flex flex-col">
                    <span className="font-medium">
                        {submitter?.name || submitterName || "Unknown"}
                    </span>
                    <span className="text-sm text-muted-foreground">
                        {submitter?.email || submitterEmail}
                    </span>
                </div>
            );
        },
    },
    {
        accessorKey: "status",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Status" />
        ),
        cell: ({ row }) => getStatusBadge(row.getValue("status")),
        filterFn: (row, id, value) => {
            return value.includes(row.getValue(id));
        },
    },
    {
        accessorKey: "createdAt",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Submitted" />
        ),
        cell: ({ row }) => {
            const date = row.getValue("createdAt") as Date;
            return (
                <div className="text-sm">
                    {format(date, "MMM dd, yyyy")}
                    <div className="text-xs text-muted-foreground">
                        {format(date, "HH:mm")}
                    </div>
                </div>
            );
        },
    },
    {
        accessorKey: "projectData.category",
        header: ({ column }) => (
            <DataTableColumnHeader column={column} title="Category" />
        ),
        cell: ({ row }) => {
            const projectData = row.original.projectData as any;
            return (
                <Badge variant="outline">
                    {projectData?.category || "Uncategorized"}
                </Badge>
            );
        },
    },
    {
        id: "actions",
        header: "Actions",
        cell: ({ row }) => {
            const submission = row.original;
            
            return (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem
                            onClick={() => navigator.clipboard.writeText(submission.id)}
                        >
                            Copy submission ID
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                            <Link href={`${siteUrls.admin.submissions}/${submission.id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                View details
                            </Link>
                        </DropdownMenuItem>
                        {submission.status === "Pending" && (
                            <>
                                <DropdownMenuItem className="text-green-600">
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Approve
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-red-600">
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Reject
                                </DropdownMenuItem>
                            </>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            );
        },
    },
];