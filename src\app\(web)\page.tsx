import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>er,
    WebPageWrapper,
} from "@/app/(web)/_components/general-components";
import { ProjectsGrid } from "@/app/(web)/_components/projects-grid";
import { buttonVariants } from "@/components/ui/button";
import { Icons } from "@/components/ui/icons";
import { siteUrls } from "@/config/urls";
import { getFeaturedProjects, getProjects } from "@/server/actions/projects";
import Link from "next/link";
import Balancer from "react-wrap-balancer";
import type { Metadata } from "next";
import { Badge } from "@/components/ui/badge";
import { Suspense } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export const metadata: Metadata = {
    title: "AI Tools Directory - Discover the Best AI Tools and Projects",
    description: "Explore a curated collection of AI tools, machine learning projects, and cutting-edge artificial intelligence solutions.",
};

export const dynamic = "force-dynamic";

/**
 * Component loading skeleton cho projects grid
 */
function ProjectsGridSkeleton() {
    return (
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {Array.from({ length: 8 }).map((_, i) => (
                <Card key={i} className="h-full">
                    <CardContent className="p-6">
                        <div className="space-y-3">
                            <Skeleton className="h-4 w-3/4" />
                            <Skeleton className="h-3 w-1/2" />
                            <Skeleton className="h-16 w-full" />
                            <div className="flex gap-2">
                                <Skeleton className="h-6 w-16" />
                                <Skeleton className="h-6 w-20" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            ))}
        </div>
    );
}

export default async function HomePage() {
    const [featuredProjects, allProjects] = await Promise.all([
        getFeaturedProjects(8),
        getProjects(),
    ]);

    return (
        <WebPageWrapper>
            <WebPageHeader
                badge="Discover AI Tools"
                title="The Ultimate AI Tools Directory"
            >
                <Balancer
                    as="p"
                    className="text-center text-base text-muted-foreground sm:text-lg"
                >
                    Explore a curated collection of AI tools, machine learning projects, 
                    and cutting-edge artificial intelligence solutions. Find the perfect 
                    tool for your next AI project.
                </Balancer>

                <div className="flex items-center gap-3">
                    <Link
                        href="/submit"
                        className={buttonVariants({ variant: "outline" })}
                    >
                        <Icons.plus className="mr-2 h-4 w-4" /> Submit Tool
                    </Link>

                    <Link
                        href="/categories"
                        className={buttonVariants()}
                    >
                        Browse Categories
                    </Link>
                </div>
            </WebPageHeader>

            {/* Featured Projects Section */}
            {featuredProjects.length > 0 && (
                <section className="space-y-8">
                    <div className="text-center space-y-2">
                        <div className="flex items-center justify-center gap-2">
                            <Badge variant="secondary" className="text-sm">
                                <Icons.star className="mr-1 h-3 w-3" />
                                Featured
                            </Badge>
                        </div>
                        <h2 className="text-2xl font-bold tracking-tight">
                            Featured AI Tools
                        </h2>
                        <p className="text-muted-foreground max-w-2xl mx-auto">
                            Hand-picked tools that are making waves in the AI community
                        </p>
                    </div>
                    
                    <Suspense fallback={<ProjectsGridSkeleton />}>
                        <ProjectsGrid projects={featuredProjects} />
                    </Suspense>
                </section>
            )}

            {/* All Projects Section */}
            <section className="space-y-8">
                <div className="text-center space-y-2">
                    <h2 className="text-2xl font-bold tracking-tight">
                        All AI Tools & Projects
                    </h2>
                    <p className="text-muted-foreground max-w-2xl mx-auto">
                        Browse our complete collection of {allProjects.length} AI tools and projects
                    </p>
                </div>
                
                <Suspense fallback={<ProjectsGridSkeleton />}>
                    <ProjectsGrid projects={allProjects} />
                </Suspense>
            </section>
        </WebPageWrapper>
    );
}
