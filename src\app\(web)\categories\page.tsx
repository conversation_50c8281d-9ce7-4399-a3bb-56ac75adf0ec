import { Suspense } from "react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Icons } from "@/components/ui/icons";
import { getCategories, getProjects } from "@/server/actions/projects";

export const metadata = {
  title: "AI Tool Categories - AI Tools Directory",
  description: "Browse AI tools by category. Find the perfect artificial intelligence solution for your needs.",
};

function CategoriesPageSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Skeleton className="h-10 w-64 mb-4" />
        <Skeleton className="h-6 w-96" />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="h-32">
            <CardContent className="p-6">
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-full mb-4" />
              <Skeleton className="h-6 w-16" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

function CategoryCard({ 
  category, 
  count, 
  description 
}: { 
  category: string; 
  count: number;
  description: string;
}) {
  return (
    <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
      <Link href={`/categories/${encodeURIComponent(category.toLowerCase())}`}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg group-hover:text-primary transition-colors">
              {category}
            </CardTitle>
            <Icons.star className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
          </div>
          <CardDescription className="text-sm">
            {description}
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <Badge variant="secondary">
              {count} {count === 1 ? 'tool' : 'tools'}
            </Badge>
            <span className="text-sm text-muted-foreground group-hover:text-primary transition-colors">
              Explore →
            </span>
          </div>
        </CardContent>
      </Link>
    </Card>
  );
}

async function CategoriesContent() {
  const [categories, allProjects] = await Promise.all([
    getCategories(),
    getProjects()
  ]);

  const categoryDescriptions: Record<string, string> = {
    "Natural Language Processing": "AI tools for text analysis, language understanding, and generation",
    "Computer Vision": "Image and video processing, recognition, and generation tools",
    "Machine Learning": "Platforms and frameworks for building ML models and algorithms",
    "Development Tools": "AI-powered coding assistants and development productivity tools",
    "Automation": "Workflow automation and process optimization solutions",
    "Analytics": "Data analysis, visualization, and business intelligence tools",
  };

  const categoriesWithCounts = categories.map(category => {
    const count = allProjects.filter(project => project.category === category.name).length;
    const description = categoryDescriptions[category.name] || `Explore ${category.name.toLowerCase()} AI tools and solutions`;
    return { category: category.name, count, description };
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">
          AI Tool Categories
        </h1>
        <p className="text-xl text-muted-foreground">
          Browse our curated collection of AI tools organized by category
        </p>
      </div>
      
      <div className="mb-8">
        <div className="flex flex-col md:flex-row gap-4 justify-between items-start md:items-center">
          <p className="text-sm text-muted-foreground">
            {categories.length} categories • {allProjects.length} total tools
          </p>
          <div className="flex gap-2">
            <Button variant="outline">
              <Icons.plus className="w-4 h-4 mr-2" />
              Submit Tool
            </Button>
            <Button variant="outline">
              <Icons.star className="w-4 h-4 mr-2" />
              Request Category
            </Button>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categoriesWithCounts.map(({ category, count, description }) => (
          <CategoryCard
            key={category}
            category={category}
            count={count}
            description={description}
          />
        ))}
      </div>
      
      <div className="mt-12 text-center">
        <h2 className="text-2xl font-semibold mb-4">Can't find what you're looking for?</h2>
        <p className="text-muted-foreground mb-6">
          We're constantly adding new categories and tools. Submit your favorite AI tool or request a new category.
        </p>
        <div className="flex gap-4 justify-center">
          <Button>
            <Icons.plus className="w-4 h-4 mr-2" />
            Submit a Tool
          </Button>
          <Button variant="outline">
            Request Category
          </Button>
        </div>
      </div>
    </div>
  );
}

export default function CategoriesPage() {
  return (
    <Suspense fallback={<CategoriesPageSkeleton />}>
      <CategoriesContent />
    </Suspense>
  );
}