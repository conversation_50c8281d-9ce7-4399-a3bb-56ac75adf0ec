import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Web<PERSON>ageHeader } from "@/app/(web)/_components/general-components";
import { ProjectCard } from "@/app/(web)/_components/project-card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ExternalLinkIcon, GithubIcon, StarIcon } from "lucide-react";
import Link from "next/link";
import type { Metadata } from "next";

export const metadata: Metadata = {
    title: "UI Comparison - Before vs After",
    description: "So s<PERSON>h thiết kế UI trước và sau khi cải thiện",
};

// Mock project data
const mockProject = {
    id: "1",
    name: "Discourse",
    slug: "discourse",
    description: "A powerful, customizable platform for building and managing online forums, fostering engaging discussions and collaborative spaces.",
    shortDescription: "Cultivate vibrant online communities with ease",
    githubUrl: "https://github.com/discourse/discourse",
    githubOwner: "discourse",
    githubRepo: "discourse",
    githubStars: 44654,
    githubForks: 8588,
    githubIssues: 234,
    githubLastCommit: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    websiteUrl: "https://discourse.org",
    demoUrl: null,
    documentationUrl: null,
    logo: "https://meta.discourse.org/uploads/default/original/1X/c4218d1c5a936cd5e46b889bb5d68fc433c4d4e4.png",
    screenshots: [],
    categoryId: "1",
    tags: ["forum", "community", "discussion", "discord-alternative", "slack-alternative"],
    primaryLanguage: "Ruby" as const,
    languages: ["Ruby", "JavaScript"],
    license: "GPL-2.0" as const,
    viewCount: 1250,
    clickCount: 340,
    status: "Published" as const,
    isFeatured: true,
    isOpenSource: true,
    submittedBy: null,
    submittedAt: null,
    reviewedBy: null,
    reviewedAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    category: "Communication",
    categorySlug: "communication",
};

// Old ProjectCard component (simplified version)
function OldProjectCard({ project }: { project: typeof mockProject }) {
    return (
        <Card className="group h-full transition-all duration-200 hover:shadow-lg hover:shadow-primary/5">
            <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                    <div className="space-y-1">
                        <CardTitle className="line-clamp-1 text-lg group-hover:text-primary transition-colors">
                            {project.name}
                        </CardTitle>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Badge variant="secondary" className="text-xs">
                                {project.category}
                            </Badge>
                            {project.isFeatured && (
                                <Badge variant="default" className="text-xs">
                                    <StarIcon className="mr-1 h-3 w-3" />
                                    Featured
                                </Badge>
                            )}
                        </div>
                    </div>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <StarIcon className="h-4 w-4" />
                        <span>{project.githubStars || 0}</span>
                    </div>
                </div>
            </CardHeader>
            
            <CardContent className="pb-4">
                <CardDescription className="line-clamp-3 text-sm leading-relaxed">
                    {project.description}
                </CardDescription>
                
                {project.tags && project.tags.length > 0 && (
                    <div className="mt-3 flex flex-wrap gap-1">
                        {project.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                            </Badge>
                        ))}
                        {project.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                                +{project.tags.length - 3}
                            </Badge>
                        )}
                    </div>
                )}
            </CardContent>
            
            <CardFooter className="pt-0">
                <div className="flex w-full items-center justify-between">
                    <div className="flex items-center gap-2">
                        {project.githubUrl && (
                            <Button variant="outline" size="sm" asChild>
                                <Link 
                                    href={project.githubUrl} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="flex items-center gap-1"
                                >
                                    <GithubIcon className="h-3 w-3" />
                                    Code
                                </Link>
                            </Button>
                        )}
                        
                        {project.websiteUrl && (
                            <Button variant="outline" size="sm" asChild>
                                <Link 
                                    href={project.websiteUrl} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="flex items-center gap-1"
                                >
                                    <ExternalLinkIcon className="h-3 w-3" />
                                    Demo
                                </Link>
                            </Button>
                        )}
                    </div>
                    
                    <Button size="sm" asChild>
                        <Link href={`/projects/${project.id}`}>
                            View Details
                        </Link>
                    </Button>
                </div>
            </CardFooter>
        </Card>
    );
}

export default function UIComparisonPage() {
    return (
        <WebPageWrapper>
            <WebPageHeader
                badge="UI Comparison"
                title="Before vs After: Project Card Redesign"
            >
                <p className="text-center text-base text-muted-foreground sm:text-lg">
                    So sánh thiết kế Project Card trước và sau khi cải thiện theo phong cách OpenAlternative/Discourse
                </p>
            </WebPageHeader>

            {/* Before Section */}
            <section className="space-y-6">
                <div>
                    <h2 className="text-2xl font-bold mb-2">Before: Original Design</h2>
                    <p className="text-muted-foreground mb-6">
                        Thiết kế ban đầu đơn giản, thiếu logo và thông tin chi tiết
                    </p>
                </div>
                <div className="max-w-md">
                    <OldProjectCard project={mockProject} />
                </div>
            </section>

            <Separator className="my-12" />

            {/* After Section */}
            <section className="space-y-6">
                <div>
                    <h2 className="text-2xl font-bold mb-2">After: Improved Design</h2>
                    <p className="text-muted-foreground mb-6">
                        Thiết kế mới với logo, GitHub stats, alternative info và layout đẹp hơn
                    </p>
                </div>
                
                <div className="grid gap-6 lg:grid-cols-2">
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Default Variant</h3>
                        <ProjectCard project={mockProject} variant="default" />
                    </div>

                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Compact Variant</h3>
                        <ProjectCard project={mockProject} variant="compact" />
                    </div>
                </div>
            </section>

            {/* Improvements */}
            <section className="space-y-6 mt-12">
                <div>
                    <h2 className="text-2xl font-bold mb-2">Key Improvements</h2>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2" variant="secondary">🎨 Logo & Avatar</Badge>
                            <p className="text-sm text-muted-foreground">
                                Thêm logo project với fallback gradient đẹp, tạo nhận diện thương hiệu
                            </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2" variant="secondary">📊 Balanced Stats</Badge>
                            <p className="text-sm text-muted-foreground">
                                Stats layout theo style OpenAlternative với spacing cân đối
                            </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2" variant="secondary">🔄 Alternative Info</Badge>
                            <p className="text-sm text-muted-foreground">
                                Thông tin "Alternative to" giúp user hiểu project thay thế cái gì
                            </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2" variant="secondary">📏 Perfect Spacing</Badge>
                            <p className="text-sm text-muted-foreground">
                                Spacing được điều chỉnh theo reference design, thoáng và cân đối
                            </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2" variant="secondary">📱 Multiple Variants</Badge>
                            <p className="text-sm text-muted-foreground">
                                Hỗ trợ variant compact cho list view và default cho grid view
                            </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2" variant="secondary">🎯 Professional Look</Badge>
                            <p className="text-sm text-muted-foreground">
                                Thiết kế professional với borders, separators và typography tốt
                            </p>
                        </div>
                    </div>
                </div>
            </section>
        </WebPageWrapper>
    );
}
