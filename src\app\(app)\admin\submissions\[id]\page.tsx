import { Suspense } from "react";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Shell } from "@/components/shell";
import { getSubmissionByIdQuery } from "@/server/actions/submissions/queries";
import { SubmissionDetails } from "../_components/submission-details";
import { Skeleton } from "@/components/ui/skeleton";

interface SubmissionDetailPageProps {
    params: {
        id: string;
    };
}

export async function generateMetadata(
    { params }: SubmissionDetailPageProps
): Promise<Metadata> {
    const submission = await getSubmissionByIdQuery({ id: params.id });
    
    if (!submission) {
        return {
            title: "Submission Not Found",
        };
    }

    const projectName = submission.projectData?.name || "Untitled Project";
    
    return {
        title: `${projectName} - Submission Details`,
        description: `Review submission details for ${projectName}`,
    };
}

export default async function SubmissionDetailPage({ params }: SubmissionDetailPageProps) {
    const submission = await getSubmissionByIdQuery({ id: params.id });

    if (!submission) {
        notFound();
    }

    return (
        <Shell variant="sidebar">
            <Suspense fallback={<SubmissionDetailsSkeleton />}>
                <SubmissionDetails submission={submission} />
            </Suspense>
        </Shell>
    );
}

function SubmissionDetailsSkeleton() {
    return (
        <div className="space-y-6">
            {/* Header Skeleton */}
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div className="space-y-2">
                    <Skeleton className="h-8 w-64" />
                    <Skeleton className="h-4 w-96" />
                </div>
                <div className="flex gap-2">
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-32" />
                </div>
            </div>

            {/* Content Skeleton */}
            <div className="grid gap-6 lg:grid-cols-3">
                <div className="lg:col-span-2 space-y-6">
                    <div className="rounded-lg border p-6">
                        <Skeleton className="h-6 w-32 mb-4" />
                        <div className="space-y-3">
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-3/4" />
                            <Skeleton className="h-4 w-1/2" />
                        </div>
                    </div>
                </div>
                <div className="space-y-6">
                    <div className="rounded-lg border p-6">
                        <Skeleton className="h-6 w-24 mb-4" />
                        <div className="space-y-3">
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-2/3" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}