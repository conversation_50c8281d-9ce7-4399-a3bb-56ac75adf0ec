import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ExternalLinkIcon, GithubIcon, StarIcon } from "lucide-react";
import Link from "next/link";
import { type ProjectWithCategory } from "@/server/actions/projects";
import { getCategoryColor } from "@/lib/avatar-utils";
import { cn } from "@/lib/utils";
import { ProjectLogo } from "./project-logo";
import { AlternativeBadge, extractAlternatives } from "./alternative-badge";
import { ProjectStatsCompact } from "./project-stats-compact";

interface ProjectCardProps {
    project: ProjectWithCategory;
    variant?: "default" | "compact";
}

/**
 * Component hiển thị thông tin project trong dạng card
 * Thiết kế theo phong cách OpenAlternative/Discourse với logo và layout sạch sẽ
 */
export function ProjectCard({ project, variant = "default" }: ProjectCardProps) {
    // Tạo category color
    const categoryColor = getCategoryColor(project.category || "default");

    // Extract alternatives
    const alternatives = extractAlternatives(project);

    if (variant === "compact") {
        return (
            <Card className="group transition-all duration-200 hover:shadow-md hover:shadow-primary/5 hover:border-primary/20">
                <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                        {/* Logo */}
                        <ProjectLogo
                            project={project}
                            size="md"
                            rounded="lg"
                        />

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                    <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors truncate">
                                        {project.name}
                                    </h3>
                                    <Badge variant="secondary" className={cn("text-xs", categoryColor)}>
                                        {project.category}
                                    </Badge>
                                    {project.isFeatured && (
                                        <Badge variant="default" className="text-xs">
                                            Featured
                                        </Badge>
                                    )}
                                </div>
                            </div>

                            <p className="text-sm text-muted-foreground line-clamp-1 mb-2">
                                {project.shortDescription || project.description}
                            </p>

                            <div className="flex items-center justify-between">
                                <ProjectStatsCompact
                                    stars={project.githubStars}
                                    forks={project.githubForks}
                                    lastCommit={project.githubLastCommit}
                                />

                                <div className="flex items-center gap-2">
                                    {project.githubUrl && (
                                        <Button variant="outline" size="sm" asChild>
                                            <Link
                                                href={project.githubUrl}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="flex items-center gap-1"
                                            >
                                                <GithubIcon className="h-3 w-3" />
                                                Code
                                            </Link>
                                        </Button>
                                    )}

                                    <Button size="sm" asChild>
                                        <Link href={`/projects/${project.slug}`}>
                                            View Details
                                        </Link>
                                    </Button>
                                </div>
                            </div>

                            {/* Alternative to */}
                            <AlternativeBadge
                                alternatives={alternatives}
                                variant="compact"
                                className="mt-2"
                            />
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="group h-full transition-all duration-200 hover:shadow-lg hover:shadow-primary/5 hover:border-primary/20 flex flex-col">
            <CardContent className="p-6 flex flex-col h-full">
                {/* Header với logo và tên */}
                <div className="flex items-center gap-3 mb-3">
                    <ProjectLogo
                        project={project}
                        size="md"
                        rounded="lg"
                    />

                    <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                            <h3 className="font-semibold text-base text-foreground group-hover:text-primary transition-colors truncate">
                                {project.name}
                            </h3>
                            {project.isFeatured && (
                                <Badge variant="default" className="text-xs ml-2 shrink-0">
                                    Featured
                                </Badge>
                            )}
                        </div>
                        <Badge variant="secondary" className={cn("text-xs mt-1", categoryColor)}>
                            {project.category}
                        </Badge>
                    </div>
                </div>

                {/* Description */}
                <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed mb-3 flex-grow">
                    {project.shortDescription || project.description}
                </p>

                {/* Alternative to section */}
                <AlternativeBadge
                    alternatives={alternatives}
                    variant="compact"
                    className="mb-3"
                />

                {/* Stats */}
                <div className="mb-4">
                    <ProjectStatsCompact
                        stars={project.githubStars}
                        forks={project.githubForks}
                        lastCommit={project.githubLastCommit}
                    />
                </div>

                {/* Tags */}
                {project.tags && project.tags.length > 0 && (
                    <div className="mb-4 flex flex-wrap gap-1">
                        {project.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                            </Badge>
                        ))}
                        {project.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                                +{project.tags.length - 3}
                            </Badge>
                        )}
                    </div>
                )}

                {/* Actions - Always at bottom */}
                <div className="flex items-center gap-2 mt-auto pt-2">
                    {project.githubUrl && (
                        <Button variant="outline" size="sm" asChild>
                            <Link
                                href={project.githubUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1"
                            >
                                <GithubIcon className="h-3 w-3" />
                                Code
                            </Link>
                        </Button>
                    )}

                    {project.websiteUrl && (
                        <Button variant="outline" size="sm" asChild>
                            <Link
                                href={project.websiteUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1"
                            >
                                <ExternalLinkIcon className="h-3 w-3" />
                                Visit
                            </Link>
                        </Button>
                    )}

                    <Button size="sm" className="ml-auto" asChild>
                        <Link href={`/projects/${project.slug}`}>
                            View Details
                        </Link>
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}