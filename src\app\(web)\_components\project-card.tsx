import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ExternalLinkIcon, GithubIcon } from "lucide-react";
import Link from "next/link";
import { type ProjectWithCategory } from "@/server/actions/projects";
import { getCategoryColor } from "@/lib/avatar-utils";
import { cn } from "@/lib/utils";
import { ProjectLogo } from "./project-logo";
import { AlternativeBadge, extractAlternatives } from "./alternative-badge";
import { GitHubStats } from "./github-stats";

interface ProjectCardProps {
    project: ProjectWithCategory;
    variant?: "default" | "compact";
}

/**
 * Component hiển thị thông tin project trong dạng card
 * Thiết kế theo phong cách OpenAlternative/Discourse với logo và layout sạch sẽ
 */
export function ProjectCard({ project, variant = "default" }: ProjectCardProps) {
    // Tạo category color
    const categoryColor = getCategoryColor(project.category || "default");

    // Extract alternatives
    const alternatives = extractAlternatives(project);

    if (variant === "compact") {
        return (
            <Card className="group transition-all duration-200 hover:shadow-md hover:shadow-primary/5 hover:border-primary/20">
                <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                        {/* Logo */}
                        <ProjectLogo
                            project={project}
                            size="md"
                            rounded="lg"
                        />

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                                <div className="min-w-0 flex-1">
                                    <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors truncate">
                                        {project.name}
                                    </h3>
                                    <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                                        {project.shortDescription || project.description}
                                    </p>
                                </div>
                            </div>

                            {/* Stats */}
                            <GitHubStats
                                stars={project.githubStars}
                                forks={project.githubForks}
                                lastCommit={project.githubLastCommit}
                                variant="compact"
                                className="mt-3"
                            />

                            {/* Alternative to */}
                            <AlternativeBadge
                                alternatives={alternatives}
                                variant="compact"
                                className="mt-2"
                            />
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="group h-full transition-all duration-200 hover:shadow-lg hover:shadow-primary/5 hover:border-primary/20">
            <CardContent className="p-6">
                {/* Header với logo và tên */}
                <div className="flex items-start gap-3 mb-4">
                    <ProjectLogo
                        project={project}
                        size="lg"
                        rounded="xl"
                    />

                    <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors truncate">
                            {project.name}
                        </h3>
                        <div className="flex items-center gap-2 mt-1">
                            <Badge variant="secondary" className={cn("text-xs", categoryColor)}>
                                {project.category}
                            </Badge>
                            {project.isFeatured && (
                                <Badge variant="default" className="text-xs">
                                    <StarIcon className="mr-1 h-3 w-3" />
                                    Featured
                                </Badge>
                            )}
                        </div>
                    </div>
                </div>

                {/* Description */}
                <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed mb-4">
                    {project.shortDescription || project.description}
                </p>

                {/* Alternative to section */}
                <AlternativeBadge
                    alternatives={alternatives}
                    variant="default"
                    className="mb-4"
                />

                <Separator className="my-4" />

                {/* Stats */}
                <GitHubStats
                    stars={project.githubStars}
                    forks={project.githubForks}
                    issues={project.githubIssues}
                    lastCommit={project.githubLastCommit}
                    variant="default"
                />

                {/* Tags */}
                {project.tags && project.tags.length > 0 && (
                    <div className="mt-4 flex flex-wrap gap-1">
                        {project.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                            </Badge>
                        ))}
                        {project.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                                +{project.tags.length - 3}
                            </Badge>
                        )}
                    </div>
                )}

                {/* Actions */}
                <div className="flex items-center gap-2 mt-6">
                    {project.githubUrl && (
                        <Button variant="outline" size="sm" asChild>
                            <Link
                                href={project.githubUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1"
                            >
                                <GithubIcon className="h-3 w-3" />
                                Code
                            </Link>
                        </Button>
                    )}

                    {project.websiteUrl && (
                        <Button variant="outline" size="sm" asChild>
                            <Link
                                href={project.websiteUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1"
                            >
                                <ExternalLinkIcon className="h-3 w-3" />
                                Visit
                            </Link>
                        </Button>
                    )}

                    <Button size="sm" className="ml-auto" asChild>
                        <Link href={`/projects/${project.slug}`}>
                            View Details
                        </Link>
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}