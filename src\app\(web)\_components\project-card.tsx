import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ExternalLinkIcon, GithubIcon, StarIcon } from "lucide-react";
import Link from "next/link";
import { type ProjectWithCategory } from "@/server/actions/projects";
import { getCategoryColor } from "@/lib/avatar-utils";
import { cn } from "@/lib/utils";
import { ProjectLogo } from "./project-logo";
import { AlternativeBadge, extractAlternatives } from "./alternative-badge";
import { ProjectStatsCompact } from "./project-stats-compact";

interface ProjectCardProps {
    project: ProjectWithCategory;
    variant?: "default" | "compact";
}

/**
 * Component hiển thị thông tin project trong dạng card
 * Thiết kế theo phong cách OpenAlternative/Discourse với logo và layout sạch sẽ
 */
export function ProjectCard({ project, variant = "default" }: ProjectCardProps) {
    // Tạo category color
    const categoryColor = getCategoryColor(project.category || "default");

    // Extract alternatives
    const alternatives = extractAlternatives(project);

    if (variant === "compact") {
        return (
            <Card className="group transition-all duration-200 hover:shadow-md hover:shadow-primary/5 hover:border-primary/20">
                <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                        {/* 1. Logo */}
                        <ProjectLogo
                            project={project}
                            size="lg"
                            rounded="lg"
                        />

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                            {/* 2. Header: Name + Featured Badge */}
                            <div className="flex items-start justify-between mb-2">
                                <h3 className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors line-clamp-1 leading-tight">
                                    {project.name}
                                </h3>
                                {project.isFeatured && (
                                    <Badge variant="default" className="text-xs ml-2 shrink-0">
                                        <StarIcon className="mr-1 h-3 w-3" />
                                        Featured
                                    </Badge>
                                )}
                            </div>

                            {/* 3. Category Badge */}
                            <Badge variant="secondary" className={cn("text-xs w-fit mb-3", categoryColor)}>
                                {project.category}
                            </Badge>

                            {/* 4. Description */}
                            <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed mb-3">
                                {project.shortDescription || project.description}
                            </p>

                            {/* 5. Alternative to */}
                            {alternatives.length > 0 && (
                                <div className="mb-3">
                                    <AlternativeBadge
                                        alternatives={alternatives}
                                        variant="compact"
                                    />
                                </div>
                            )}

                            {/* 6. Stats Section - Horizontal layout for compact */}
                            <div className="flex items-center gap-6 py-3 mb-3 border-y border-border/30 text-sm">
                                {/* Stars */}
                                <div className="flex items-center gap-2">
                                    <StarIcon className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                    <span className="text-muted-foreground">Stars</span>
                                    <span className="font-semibold text-foreground">
                                        {(project.githubStars || 0).toLocaleString()}
                                    </span>
                                </div>

                                {/* Forks */}
                                <div className="flex items-center gap-2">
                                    <GithubIcon className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-muted-foreground">Forks</span>
                                    <span className="font-semibold text-foreground">
                                        {(project.githubForks || 0).toLocaleString()}
                                    </span>
                                </div>

                                {/* Last Commit */}
                                {project.githubLastCommit && (
                                    <div className="flex items-center gap-2">
                                        <div className="h-4 w-4 rounded-full border-2 border-green-500 bg-green-500/20"></div>
                                        <span className="text-muted-foreground">Last commit</span>
                                        <span className="font-medium text-foreground text-xs">
                                            {(() => {
                                                const date = new Date(project.githubLastCommit);
                                                const now = new Date();
                                                const diffTime = Math.abs(now.getTime() - date.getTime());
                                                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                                                if (diffDays === 1) return "1 day ago";
                                                if (diffDays < 7) return `${diffDays} days ago`;
                                                if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
                                                return `${Math.ceil(diffDays / 30)} months ago`;
                                            })()}
                                        </span>
                                    </div>
                                )}
                            </div>

                            {/* 7. Actions */}
                            <div className="flex items-center gap-2">
                                {project.githubUrl && (
                                    <Button variant="outline" size="sm" asChild>
                                        <Link
                                            href={project.githubUrl}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="flex items-center gap-1"
                                        >
                                            <GithubIcon className="h-3 w-3" />
                                            Code
                                        </Link>
                                    </Button>
                                )}

                                {project.websiteUrl && (
                                    <Button variant="outline" size="sm" asChild>
                                        <Link
                                            href={project.websiteUrl}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="flex items-center gap-1"
                                        >
                                            <ExternalLinkIcon className="h-3 w-3" />
                                            Visit
                                        </Link>
                                    </Button>
                                )}

                                <Button size="sm" className="ml-auto" asChild>
                                    <Link href={`/projects/${project.slug}`}>
                                        View Details
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="group h-full transition-all duration-200 hover:shadow-lg hover:shadow-primary/5 hover:border-primary/20 flex flex-col">
            <CardContent className="p-6 flex flex-col h-full">
                {/* 1. Header Section: Logo + Name + Featured Badge */}
                <div className="flex items-start gap-3 mb-4">
                    <ProjectLogo
                        project={project}
                        size="lg"
                        rounded="lg"
                    />

                    <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                            <h3 className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors line-clamp-1 leading-tight">
                                {project.name}
                            </h3>
                            {project.isFeatured && (
                                <Badge variant="default" className="text-xs ml-2 shrink-0">
                                    <StarIcon className="mr-1 h-3 w-3" />
                                    Featured
                                </Badge>
                            )}
                        </div>

                        {/* 2. Category Badge */}
                        <Badge variant="secondary" className={cn("text-xs w-fit", categoryColor)}>
                            {project.category}
                        </Badge>
                    </div>
                </div>

                {/* 3. Description */}
                <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed mb-4 flex-grow">
                    {project.shortDescription || project.description}
                </p>

                {/* 4. Alternative to Section */}
                {alternatives.length > 0 && (
                    <div className="mb-4">
                        <AlternativeBadge
                            alternatives={alternatives}
                            variant="default"
                        />
                    </div>
                )}

                {/* 5. GitHub Stats Section */}
                <div className="space-y-3 py-4 mb-4 border-y border-border/30">
                    {/* Stars */}
                    <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <StarIcon className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span>Stars</span>
                        </div>
                        <span className="font-semibold text-foreground">
                            {(project.githubStars || 0).toLocaleString()}
                        </span>
                    </div>

                    {/* Forks */}
                    <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <GithubIcon className="h-4 w-4" />
                            <span>Forks</span>
                        </div>
                        <span className="font-semibold text-foreground">
                            {(project.githubForks || 0).toLocaleString()}
                        </span>
                    </div>

                    {/* Last Commit */}
                    {project.githubLastCommit && (
                        <div className="flex items-center justify-between text-sm">
                            <div className="flex items-center gap-2 text-muted-foreground">
                                <div className="h-4 w-4 rounded-full border-2 border-green-500 bg-green-500/20"></div>
                                <span>Last commit</span>
                            </div>
                            <span className="font-medium text-foreground text-xs">
                                {(() => {
                                    const date = new Date(project.githubLastCommit);
                                    const now = new Date();
                                    const diffTime = Math.abs(now.getTime() - date.getTime());
                                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                                    if (diffDays === 1) return "1 day ago";
                                    if (diffDays < 7) return `${diffDays} days ago`;
                                    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
                                    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;
                                    return `${Math.ceil(diffDays / 365)} years ago`;
                                })()}
                            </span>
                        </div>
                    )}
                </div>

                {/* 6. Tags Section */}
                {project.tags && project.tags.length > 0 && (
                    <div className="mb-6 flex flex-wrap gap-2">
                        {project.tags.slice(0, 4).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs hover:bg-primary/5">
                                {tag}
                            </Badge>
                        ))}
                        {project.tags.length > 4 && (
                            <Badge variant="outline" className="text-xs bg-muted/50">
                                +{project.tags.length - 4}
                            </Badge>
                        )}
                    </div>
                )}

                {/* 7. Actions Section - Always at bottom */}
                <div className="flex items-center gap-2 mt-auto pt-2">
                    {project.githubUrl && (
                        <Button variant="outline" size="sm" asChild>
                            <Link
                                href={project.githubUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1"
                            >
                                <GithubIcon className="h-3 w-3" />
                                Code
                            </Link>
                        </Button>
                    )}

                    {project.websiteUrl && (
                        <Button variant="outline" size="sm" asChild>
                            <Link
                                href={project.websiteUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1"
                            >
                                <ExternalLinkIcon className="h-3 w-3" />
                                Visit
                            </Link>
                        </Button>
                    )}

                    <Button size="sm" className="ml-auto" asChild>
                        <Link href={`/projects/${project.slug}`}>
                            View Details
                        </Link>
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}