import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ExternalLinkIcon, GithubIcon, StarIcon } from "lucide-react";
import Link from "next/link";
import { type ProjectWithCategory } from "@/server/actions/projects";
import { getCategoryColor } from "@/lib/avatar-utils";
import { cn } from "@/lib/utils";
import { ProjectLogo } from "./project-logo";
import { AlternativeBadge, extractAlternatives } from "./alternative-badge";
import { ProjectStatsCompact } from "./project-stats-compact";

interface ProjectCardProps {
    project: ProjectWithCategory;
    variant?: "default" | "compact";
}

/**
 * Component hiển thị thông tin project trong dạng card
 * Thiết kế theo phong cách OpenAlternative/Discourse với logo và layout sạch sẽ
 */
export function ProjectCard({ project, variant = "default" }: ProjectCardProps) {
    // Tạo category color
    const categoryColor = getCategoryColor(project.category || "default");

    // Extract alternatives
    const alternatives = extractAlternatives(project);

    if (variant === "compact") {
        return (
            <Card className="group transition-all duration-200 hover:shadow-md hover:shadow-primary/5 hover:border-primary/20">
                <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                        {/* Logo */}
                        <ProjectLogo
                            project={project}
                            size="md"
                            rounded="lg"
                        />

                        {/* Content */}
                        <div className="flex-1 min-w-0 space-y-4">
                            {/* Header */}
                            <div className="space-y-1">
                                <div className="flex items-start justify-between">
                                    <h3 className="font-semibold text-base text-foreground group-hover:text-primary transition-colors line-clamp-1">
                                        {project.name}
                                    </h3>
                                    {project.isFeatured && (
                                        <Badge variant="default" className="text-xs ml-2 shrink-0">
                                            <StarIcon className="mr-1 h-3 w-3" />
                                            Featured
                                        </Badge>
                                    )}
                                </div>
                                <Badge variant="secondary" className={cn("text-xs w-fit", categoryColor)}>
                                    {project.category}
                                </Badge>
                            </div>

                            {/* Description */}
                            <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                                {project.shortDescription || project.description}
                            </p>

                            {/* Alternative to */}
                            {alternatives.length > 0 && (
                                <AlternativeBadge
                                    alternatives={alternatives}
                                    variant="compact"
                                />
                            )}

                            {/* Stats - Reference style */}
                            <div className="grid grid-cols-3 gap-4 py-3 border-y border-border/30 text-sm">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2 text-muted-foreground">
                                        <StarIcon className="h-3 w-3" />
                                        <span className="text-xs">Stars</span>
                                    </div>
                                    <span className="font-medium text-xs">{(project.githubStars || 0).toLocaleString()}</span>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2 text-muted-foreground">
                                        <GithubIcon className="h-3 w-3" />
                                        <span className="text-xs">Forks</span>
                                    </div>
                                    <span className="font-medium text-xs">{(project.githubForks || 0).toLocaleString()}</span>
                                </div>

                                {project.githubLastCommit && (
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2 text-muted-foreground">
                                            <div className="h-3 w-3 rounded-full border border-muted-foreground/50"></div>
                                            <span className="text-xs">Last commit</span>
                                        </div>
                                        <span className="font-medium text-xs">
                                            {new Date(project.githubLastCommit).toLocaleDateString()}
                                        </span>
                                    </div>
                                )}
                            </div>

                            {/* Actions */}
                            <div className="flex items-center gap-2">
                                {project.githubUrl && (
                                    <Button variant="outline" size="sm" asChild>
                                        <Link
                                            href={project.githubUrl}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="flex items-center gap-1"
                                        >
                                            <GithubIcon className="h-3 w-3" />
                                            Code
                                        </Link>
                                    </Button>
                                )}

                                <Button size="sm" className="ml-auto" asChild>
                                    <Link href={`/projects/${project.slug}`}>
                                        View Details
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="group h-full transition-all duration-200 hover:shadow-lg hover:shadow-primary/5 hover:border-primary/20 flex flex-col">
            <CardContent className="p-6 flex flex-col h-full space-y-4">
                {/* Header với logo và tên */}
                <div className="flex items-start gap-3">
                    <ProjectLogo
                        project={project}
                        size="md"
                        rounded="lg"
                    />

                    <div className="flex-1 min-w-0 space-y-1">
                        <div className="flex items-start justify-between">
                            <h3 className="font-semibold text-base text-foreground group-hover:text-primary transition-colors line-clamp-1">
                                {project.name}
                            </h3>
                            {project.isFeatured && (
                                <Badge variant="default" className="text-xs ml-2 shrink-0">
                                    <StarIcon className="mr-1 h-3 w-3" />
                                    Featured
                                </Badge>
                            )}
                        </div>
                        <Badge variant="secondary" className={cn("text-xs w-fit", categoryColor)}>
                            {project.category}
                        </Badge>
                    </div>
                </div>

                {/* Description */}
                <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed flex-grow">
                    {project.shortDescription || project.description}
                </p>

                {/* Alternative to section */}
                {alternatives.length > 0 && (
                    <AlternativeBadge
                        alternatives={alternatives}
                        variant="compact"
                    />
                )}

                {/* Stats - Styled like the reference */}
                <div className="space-y-3 py-3 border-y border-border/30">
                    <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <StarIcon className="h-4 w-4" />
                            <span>Stars</span>
                        </div>
                        <span className="font-medium">{(project.githubStars || 0).toLocaleString()}</span>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <GithubIcon className="h-4 w-4" />
                            <span>Forks</span>
                        </div>
                        <span className="font-medium">{(project.githubForks || 0).toLocaleString()}</span>
                    </div>

                    {project.githubLastCommit && (
                        <div className="flex items-center justify-between text-sm">
                            <div className="flex items-center gap-2 text-muted-foreground">
                                <div className="h-4 w-4 rounded-full border-2 border-muted-foreground/50"></div>
                                <span>Last commit</span>
                            </div>
                            <span className="font-medium text-xs">
                                {new Date(project.githubLastCommit).toLocaleDateString()}
                            </span>
                        </div>
                    )}
                </div>

                {/* Tags */}
                {project.tags && project.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                        {project.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                            </Badge>
                        ))}
                        {project.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                                +{project.tags.length - 3}
                            </Badge>
                        )}
                    </div>
                )}

                {/* Actions - Always at bottom */}
                <div className="flex items-center gap-2 mt-auto">
                    {project.githubUrl && (
                        <Button variant="outline" size="sm" asChild>
                            <Link
                                href={project.githubUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1"
                            >
                                <GithubIcon className="h-3 w-3" />
                                Code
                            </Link>
                        </Button>
                    )}

                    {project.websiteUrl && (
                        <Button variant="outline" size="sm" asChild>
                            <Link
                                href={project.websiteUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-1"
                            >
                                <ExternalLinkIcon className="h-3 w-3" />
                                Visit
                            </Link>
                        </Button>
                    )}

                    <Button size="sm" className="ml-auto" asChild>
                        <Link href={`/projects/${project.slug}`}>
                            View Details
                        </Link>
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}