import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getProjectAvatar } from "@/lib/avatar-utils";
import { cn } from "@/lib/utils";

interface ProjectLogoProps {
    project: {
        name: string;
        logo?: string | null;
        githubUrl?: string | null;
    };
    size?: "sm" | "md" | "lg" | "xl";
    className?: string;
    rounded?: "sm" | "md" | "lg" | "xl" | "full";
}

/**
 * Component hiển thị logo của project với fallback đẹp
 * Hỗ trợ nhiều kích thước và bo góc khác nhau
 */
export function ProjectLogo({ 
    project, 
    size = "md", 
    className,
    rounded = "lg" 
}: ProjectLogoProps) {
    const avatarInfo = getProjectAvatar(project);

    const sizeClasses = {
        sm: "h-8 w-8",
        md: "h-10 w-10", 
        lg: "h-12 w-12",
        xl: "h-16 w-16",
    };

    const roundedClasses = {
        sm: "rounded-sm",
        md: "rounded-md",
        lg: "rounded-lg",
        xl: "rounded-xl", 
        full: "rounded-full",
    };

    const textSizeClasses = {
        sm: "text-xs",
        md: "text-sm",
        lg: "text-base",
        xl: "text-lg",
    };

    return (
        <Avatar className={cn(
            sizeClasses[size],
            roundedClasses[rounded],
            "shrink-0",
            className
        )}>
            <AvatarImage 
                src={avatarInfo.src} 
                alt={`${project.name} logo`}
                className={roundedClasses[rounded]}
            />
            <AvatarFallback className={cn(
                roundedClasses[rounded],
                "bg-gradient-to-br text-white font-semibold",
                textSizeClasses[size],
                avatarInfo.gradient
            )}>
                {avatarInfo.fallback}
            </AvatarFallback>
        </Avatar>
    );
}
