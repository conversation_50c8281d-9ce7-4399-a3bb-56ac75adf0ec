import { relations, sql } from "drizzle-orm";
import {
    boolean,
    index,
    integer,
    jsonb,
    pgEnum,
    pgTableCreator,
    primaryKey,
    text,
    timestamp,
    varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { type AdapterAccount } from "next-auth/adapters";
import { z } from "zod";

/**
 * This is an example of how to use the multi-project schema feature of Drizzle ORM. Use the same
 * database instance for multiple projects.
 *
 * @see https://orm.drizzle.team/docs/goodies#multi-project-schema
 */
export const createTable = pgTableCreator(
    (name) => `launchmvpfast-saas-starterkit_${name}`,
);

export const usersRoleEnum = pgEnum("role", ["User", "Admin", "Super Admin"]);

export const users = createTable("user", {
    id: varchar("id", { length: 255 }).notNull().primary<PERSON>ey(),
    name: varchar("name", { length: 255 }),
    email: varchar("email", { length: 255 }).notNull(),
    emailVerified: timestamp("emailVerified", {
        mode: "date",
    }).default(sql`CURRENT_TIMESTAMP`),
    image: varchar("image", { length: 255 }),
    role: usersRoleEnum("role").default("User").notNull(),
    isNewUser: boolean("isNewUser").default(true).notNull(),
    createdAt: timestamp("createdAt", { mode: "date" }).notNull().defaultNow(),
});

export const usersRelations = relations(users, ({ many }) => ({
    accounts: many(accounts),
    membersToOrganizations: many(membersToOrganizations),
    feedback: many(feedback),
    submittedProjects: many(projects, { relationName: "submittedProjects" }),
    reviewedProjects: many(projects, { relationName: "reviewedProjects" }),
    submissions: many(projectSubmissions, { relationName: "submissions" }),
    reviewedSubmissions: many(projectSubmissions, { relationName: "reviewedSubmissions" }),
}));

export const userInsertSchema = createInsertSchema(users, {
    name: z
        .string()
        .trim()
        .min(3, "Name must be at least 3 characters long")
        .max(50, "Name must be at most 50 characters long"),
    email: z.string().email(),
    image: z.string().url(),
});

export const accounts = createTable(
    "account",
    {
        userId: varchar("userId", { length: 255 })
            .notNull()
            .references(() => users.id),
        type: varchar("type", { length: 255 })
            .$type<AdapterAccount["type"]>()
            .notNull(),
        provider: varchar("provider", { length: 255 }).notNull(),
        providerAccountId: varchar("providerAccountId", {
            length: 255,
        }).notNull(),
        refresh_token: text("refresh_token"),
        access_token: text("access_token"),
        expires_at: integer("expires_at"),
        token_type: varchar("token_type", { length: 255 }),
        scope: varchar("scope", { length: 255 }),
        id_token: text("id_token"),
        session_state: varchar("session_state", { length: 255 }),
    },
    (account) => ({
        compoundKey: primaryKey({
            columns: [account.provider, account.providerAccountId],
        }),
        userIdIdx: index("account_userId_idx").on(account.userId),
    }),
);

export const accountsRelations = relations(accounts, ({ one }) => ({
    user: one(users, { fields: [accounts.userId], references: [users.id] }),
}));

export const sessions = createTable(
    "session",
    {
        sessionToken: varchar("sessionToken", { length: 255 })
            .notNull()
            .primaryKey(),
        userId: varchar("userId", { length: 255 })
            .notNull()
            .references(() => users.id),
        expires: timestamp("expires", { mode: "date" }).notNull(),
    },
    (session) => ({
        userIdIdx: index("session_userId_idx").on(session.userId),
    }),
);

export const sessionsRelations = relations(sessions, ({ one }) => ({
    user: one(users, { fields: [sessions.userId], references: [users.id] }),
}));

export const verificationTokens = createTable(
    "verificationToken",
    {
        identifier: varchar("identifier", { length: 255 }).notNull(),
        token: varchar("token", { length: 255 }).notNull(),
        expires: timestamp("expires", { mode: "date" }).notNull(),
    },
    (vt) => ({
        compoundKey: primaryKey({ columns: [vt.identifier, vt.token] }),
    }),
);

export const organizations = createTable("organization", {
    id: varchar("id", { length: 255 })
        .notNull()
        .primaryKey()
        .default(sql`gen_random_uuid()`),
    name: varchar("name", { length: 255 }).notNull(),
    email: varchar("email", { length: 255 }).notNull(),
    image: varchar("image", { length: 255 }),
    createdAt: timestamp("createdAt", { mode: "date" }).notNull().defaultNow(),
    ownerId: varchar("ownerId", { length: 255 })
        .notNull()
        .references(() => users.id, { onDelete: "cascade" }),
});

export const createOrgInsertSchema = createInsertSchema(organizations, {
    name: z
        .string()
        .min(3, "Name must be at least 3 characters long")
        .max(50, "Name must be at most 50 characters long"),
    image: z.string().url({ message: "Invalid image URL" }),
});

export const organizationsRelations = relations(
    organizations,
    ({ one, many }) => ({
        owner: one(users, {
            fields: [organizations.ownerId],
            references: [users.id],
        }),
        membersToOrganizations: many(membersToOrganizations),
        subscriptions: one(subscriptions, {
            fields: [organizations.id],
            references: [subscriptions.orgId],
        }),
    }),
);

export const membersToOrganizationsRoleEnum = pgEnum("org-member-role", [
    "Viewer",
    "Developer",
    "Billing",
    "Admin",
]);

export const membersToOrganizations = createTable(
    "membersToOrganizations",
    {
        id: varchar("id", { length: 255 }).default(sql`gen_random_uuid()`),
        memberId: varchar("memberId", { length: 255 })
            .notNull()
            .references(() => users.id, { onDelete: "cascade" }),
        memberEmail: varchar("memberEmail", { length: 255 }).notNull(),
        organizationId: varchar("organizationId", { length: 255 })
            .notNull()
            .references(() => organizations.id, { onDelete: "cascade" }),
        role: membersToOrganizationsRoleEnum("role")
            .default("Viewer")
            .notNull(),
        createdAt: timestamp("createdAt", { mode: "date" })
            .notNull()
            .defaultNow(),
    },
    (mto) => ({
        compoundKey: primaryKey({
            columns: [mto.id, mto.memberId, mto.organizationId],
        }),
    }),
);

export const membersToOrganizationsRelations = relations(
    membersToOrganizations,
    ({ one }) => ({
        member: one(users, {
            fields: [membersToOrganizations.memberId],
            references: [users.id],
        }),
        organization: one(organizations, {
            fields: [membersToOrganizations.organizationId],
            references: [organizations.id],
        }),
    }),
);

export const membersToOrganizationsInsertSchema = createInsertSchema(
    membersToOrganizations,
);

export const orgRequests = createTable(
    "orgRequest",
    {
        id: varchar("id", { length: 255 })
            .notNull()
            .primaryKey()
            .default(sql`gen_random_uuid()`),
        userId: varchar("userId", { length: 255 })
            .notNull()
            .references(() => users.id, { onDelete: "cascade" }),

        organizationId: varchar("organizationId", {
            length: 255,
        })
            .notNull()
            .references(() => organizations.id, { onDelete: "cascade" }),
        createdAt: timestamp("createdAt", { mode: "date" })
            .notNull()
            .defaultNow(),
    },
    (or) => ({
        orgIdIdx: index("orgRequest_organizationId_idx").on(or.organizationId),
    }),
);

export const orgRequestsRelations = relations(orgRequests, ({ one }) => ({
    user: one(users, { fields: [orgRequests.userId], references: [users.id] }),
    organization: one(organizations, {
        fields: [orgRequests.organizationId],
        references: [organizations.id],
    }),
}));

export const orgRequestInsertSchema = createInsertSchema(orgRequests);

// Feedback schema

export const feedbackLabelEnum = pgEnum("feedback-label", [
    "Issue",
    "Idea",
    "Question",
    "Complaint",
    "Feature Request",
    "Other",
]);

export const feedbackStatusEnum = pgEnum("feedback-status", [
    "Open",
    "In Progress",
    "Closed",
]);

export const feedback = createTable("feedback", {
    id: varchar("id", { length: 255 })
        .notNull()
        .primaryKey()
        .default(sql`gen_random_uuid()`),
    userId: varchar("userId", { length: 255 })
        .notNull()
        .references(() => users.id, { onDelete: "cascade" }),
    title: varchar("title", { length: 255 }),
    message: text("message").notNull(),
    label: feedbackLabelEnum("label").notNull(),
    status: feedbackStatusEnum("status").default("Open").notNull(),
    createdAt: timestamp("createdAt", { mode: "date" }).notNull().defaultNow(),
});

export const feedbackRelations = relations(feedback, ({ one }) => ({
    user: one(users, { fields: [feedback.userId], references: [users.id] }),
}));

export const feedbackInsertSchema = createInsertSchema(feedback, {
    title: z
        .string()
        .min(3, "Title is too short")
        .max(255, "Title is too long"),
    message: z
        .string()
        .min(10, "Message is too short")
        .max(1000, "Message is too long"),
});

export const feedbackSelectSchema = createSelectSchema(feedback, {
    title: z
        .string()
        .min(3, "Title is too short")
        .max(255, "Title is too long"),
    message: z
        .string()
        .min(10, "Message is too short")
        .max(1000, "Message is too long"),
});

export const webhookEvents = createTable("webhookEvent", {
    id: varchar("id", { length: 255 })
        .notNull()
        .primaryKey()
        .default(sql`gen_random_uuid()`),
    createdAt: timestamp("createdAt", { mode: "date" }).notNull().defaultNow(),
    eventName: text("eventName").notNull(),
    processed: boolean("processed").default(false),
    body: jsonb("body").notNull(),
    processingError: text("processingError"),
});

export const subscriptions = createTable("subscription", {
    id: varchar("id", { length: 255 })
        .notNull()
        .primaryKey()
        .default(sql`gen_random_uuid()`),
    lemonSqueezyId: text("lemonSqueezyId").unique().notNull(),
    orderId: integer("orderId").notNull(),
    orgId: text("orgId")
        .notNull()
        .references(() => organizations.id, { onDelete: "cascade" }),
    variantId: integer("variantId").notNull(),
});

export const subscriptionsRelations = relations(subscriptions, ({ one }) => ({
    organization: one(organizations, {
        fields: [subscriptions.orgId],
        references: [organizations.id],
    }),
}));

export const waitlistUsers = createTable("waitlistUser", {
    id: varchar("id", { length: 255 })
        .notNull()
        .primaryKey()
        .default(sql`gen_random_uuid()`),
    email: varchar("email", { length: 255 }).notNull().unique(),
    name: varchar("name", { length: 255 }).notNull(),
    createdAt: timestamp("createdAt", { mode: "date" }).notNull().defaultNow(),
});

export const waitlistUsersSchema = createInsertSchema(waitlistUsers, {
    email: z.string().email("Email must be a valid email address"),
    name: z.string().min(3, "Name must be at least 3 characters long"),
});

// AI Tools Directory Schema

/**
 * Categories for AI tools/projects
 * Ví dụ: Machine Learning, Computer Vision, NLP, Data Science, Robotics
 */
export const categories = createTable("category", {
    id: varchar("id", { length: 255 })
        .notNull()
        .primaryKey()
        .default(sql`gen_random_uuid()`),
    name: varchar("name", { length: 100 }).notNull().unique(),
    slug: varchar("slug", { length: 100 }).notNull().unique(),
    description: text("description"),
    icon: varchar("icon", { length: 255 }), // Icon name hoặc emoji
    color: varchar("color", { length: 7 }).default("#3B82F6"), // Hex color
    isActive: boolean("isActive").default(true).notNull(),
    createdAt: timestamp("createdAt", { mode: "date" }).notNull().defaultNow(),
    updatedAt: timestamp("updatedAt", { mode: "date" }).notNull().defaultNow(),
});

export const categoriesRelations = relations(categories, ({ many }) => ({
    projects: many(projects),
}));

export const categoryInsertSchema = createInsertSchema(categories, {
    name: z
        .string()
        .min(2, "Category name must be at least 2 characters long")
        .max(100, "Category name must be at most 100 characters long"),
    slug: z
        .string()
        .min(2, "Slug must be at least 2 characters long")
        .max(100, "Slug must be at most 100 characters long")
        .regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
    description: z.string().max(500, "Description must be at most 500 characters long").optional(),
    color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Color must be a valid hex color").optional(),
});

/**
 * Project status enum
 */
export const projectStatusEnum = pgEnum("project-status", [
    "Draft",
    "Published",
    "Archived",
    "Under Review",
]);

/**
 * License types enum
 */
export const licenseTypeEnum = pgEnum("license-type", [
    "MIT",
    "Apache-2.0",
    "GPL-3.0",
    "BSD-3-Clause",
    "ISC",
    "MPL-2.0",
    "LGPL-3.0",
    "Unlicense",
    "Proprietary",
    "Other",
]);

/**
 * Programming languages enum
 */
export const programmingLanguageEnum = pgEnum("programming-language", [
    "Python",
    "JavaScript",
    "TypeScript",
    "Java",
    "C++",
    "C#",
    "Go",
    "Rust",
    "Swift",
    "Kotlin",
    "R",
    "MATLAB",
    "Scala",
    "Julia",
    "PHP",
    "Ruby",
    "Other",
]);

/**
 * AI Tools/Projects table
 */
export const projects = createTable(
    "project",
    {
        id: varchar("id", { length: 255 })
            .notNull()
            .primaryKey()
            .default(sql`gen_random_uuid()`),
        name: varchar("name", { length: 200 }).notNull(),
        slug: varchar("slug", { length: 200 }).notNull().unique(),
        description: text("description").notNull(),
        shortDescription: varchar("shortDescription", { length: 300 }),
        
        // GitHub integration
        githubUrl: varchar("githubUrl", { length: 500 }).notNull(),
        githubOwner: varchar("githubOwner", { length: 100 }),
        githubRepo: varchar("githubRepo", { length: 100 }),
        githubStars: integer("githubStars").default(0),
        githubForks: integer("githubForks").default(0),
        githubIssues: integer("githubIssues").default(0),
        githubLastCommit: timestamp("githubLastCommit", { mode: "date" }),
        
        // Project details
        websiteUrl: varchar("websiteUrl", { length: 500 }),
        demoUrl: varchar("demoUrl", { length: 500 }),
        documentationUrl: varchar("documentationUrl", { length: 500 }),
        
        // Media
        logo: varchar("logo", { length: 500 }), // URL to logo image
        screenshots: jsonb("screenshots").$type<string[]>().default([]), // Array of screenshot URLs
        
        // Categorization
        categoryId: varchar("categoryId", { length: 255 })
            .notNull()
            .references(() => categories.id, { onDelete: "cascade" }),
        tags: jsonb("tags").$type<string[]>().default([]), // Array of tags
        
        // Technical details
        primaryLanguage: programmingLanguageEnum("primaryLanguage"),
        languages: jsonb("languages").$type<string[]>().default([]), // Array of programming languages
        license: licenseTypeEnum("license"),
        
        // Metrics
        viewCount: integer("viewCount").default(0),
        clickCount: integer("clickCount").default(0),
        
        // Status and metadata
        status: projectStatusEnum("status").default("Published").notNull(),
        isFeatured: boolean("isFeatured").default(false),
        isOpenSource: boolean("isOpenSource").default(true),
        
        // Submission info
        submittedBy: varchar("submittedBy", { length: 255 }).references(() => users.id),
        submittedAt: timestamp("submittedAt", { mode: "date" }),
        reviewedBy: varchar("reviewedBy", { length: 255 }).references(() => users.id),
        reviewedAt: timestamp("reviewedAt", { mode: "date" }),
        
        // Timestamps
        createdAt: timestamp("createdAt", { mode: "date" }).notNull().defaultNow(),
        updatedAt: timestamp("updatedAt", { mode: "date" }).notNull().defaultNow(),
    },
    (project) => ({
        categoryIdIdx: index("project_categoryId_idx").on(project.categoryId),
        statusIdx: index("project_status_idx").on(project.status),
        featuredIdx: index("project_featured_idx").on(project.isFeatured),
        githubUrlIdx: index("project_githubUrl_idx").on(project.githubUrl),
        slugIdx: index("project_slug_idx").on(project.slug),
    }),
);

export const projectsRelations = relations(projects, ({ one, many }) => ({
    category: one(categories, {
        fields: [projects.categoryId],
        references: [categories.id],
    }),
    submitter: one(users, {
        fields: [projects.submittedBy],
        references: [users.id],
        relationName: "submittedProjects",
    }),
    reviewer: one(users, {
        fields: [projects.reviewedBy],
        references: [users.id],
        relationName: "reviewedProjects",
    }),
    submissions: many(projectSubmissions),
}));

export const projectInsertSchema = createInsertSchema(projects, {
    name: z
        .string()
        .min(3, "Project name must be at least 3 characters long")
        .max(200, "Project name must be at most 200 characters long"),
    slug: z
        .string()
        .min(3, "Slug must be at least 3 characters long")
        .max(200, "Slug must be at most 200 characters long")
        .regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
    description: z
        .string()
        .min(10, "Description must be at least 10 characters long")
        .max(5000, "Description must be at most 5000 characters long"),
    shortDescription: z
        .string()
        .max(300, "Short description must be at most 300 characters long")
        .optional(),
    githubUrl: z
        .string()
        .url("GitHub URL must be a valid URL")
        .regex(/^https:\/\/github\.com\/[\w.-]+\/[\w.-]+\/?$/, "Must be a valid GitHub repository URL"),
    websiteUrl: z.string().url("Website URL must be a valid URL").optional(),
    demoUrl: z.string().url("Demo URL must be a valid URL").optional(),
    documentationUrl: z.string().url("Documentation URL must be a valid URL").optional(),
    logo: z.string().url("Logo must be a valid URL").optional(),
    screenshots: z.array(z.string().url()).optional(),
    tags: z.array(z.string()).optional(),
    languages: z.array(z.string()).optional(),
});

export const projectSelectSchema = createSelectSchema(projects);

/**
 * Project submissions table - for user-submitted projects
 */
export const submissionStatusEnum = pgEnum("submission-status", [
    "Pending",
    "Under Review",
    "Approved",
    "Rejected",
    "Needs Changes",
]);

export const projectSubmissions = createTable(
    "projectSubmission",
    {
        id: varchar("id", { length: 255 })
            .notNull()
            .primaryKey()
            .default(sql`gen_random_uuid()`),
        
        // Project data (JSON format for flexibility)
        projectData: jsonb("projectData").notNull(), // Contains all project fields
        
        // Submission metadata
        submittedBy: varchar("submittedBy", { length: 255 })
            .notNull()
            .references(() => users.id, { onDelete: "cascade" }),
        submitterEmail: varchar("submitterEmail", { length: 255 }).notNull(),
        submitterName: varchar("submitterName", { length: 255 }),
        
        // Review process
        status: submissionStatusEnum("status").default("Pending").notNull(),
        reviewNotes: text("reviewNotes"),
        reviewedBy: varchar("reviewedBy", { length: 255 }).references(() => users.id),
        reviewedAt: timestamp("reviewedAt", { mode: "date" }),
        
        // If approved, link to created project
        projectId: varchar("projectId", { length: 255 }).references(() => projects.id),
        
        // Timestamps
        createdAt: timestamp("createdAt", { mode: "date" }).notNull().defaultNow(),
        updatedAt: timestamp("updatedAt", { mode: "date" }).notNull().defaultNow(),
    },
    (submission) => ({
        submittedByIdx: index("submission_submittedBy_idx").on(submission.submittedBy),
        statusIdx: index("submission_status_idx").on(submission.status),
        reviewedByIdx: index("submission_reviewedBy_idx").on(submission.reviewedBy),
    }),
);

export const projectSubmissionsRelations = relations(projectSubmissions, ({ one }) => ({
    submitter: one(users, {
        fields: [projectSubmissions.submittedBy],
        references: [users.id],
        relationName: "submissions",
    }),
    reviewer: one(users, {
        fields: [projectSubmissions.reviewedBy],
        references: [users.id],
        relationName: "reviewedSubmissions",
    }),
    project: one(projects, {
        fields: [projectSubmissions.projectId],
        references: [projects.id],
    }),
}));

export const submissionInsertSchema = createInsertSchema(projectSubmissions, {
    submitterEmail: z.string().email("Must be a valid email address"),
    submitterName: z.string().min(2, "Name must be at least 2 characters long").optional(),
    reviewNotes: z.string().max(2000, "Review notes must be at most 2000 characters long").optional(),
});
