---
title: "Introduction"
description: "launchmvpfast is an open-source Next.js SaaS Starterkit/Boilerplate designed to expedite the development process of Software as a Service (SaaS) applications. Launch your MVP in days."
---

## Creator

-   Github [@alifarooq9](https://www.github.com/alifarooq9)
-   Twitter [@AliFarooqDev](https://www.twitter.com/AliFarooqDev)

## Built with

-   [Create T3 App](https://create.t3.gg/)
-   [Next.js](https://nextjs.org/)
-   [Drizzle](https://orm.drizzle.team/)
-   [NextAuth.js](https://next-auth.js.org/)
-   [Resend](https://resend.com/)
-   [Uploadthing](https://uploadthing.com/)
-   [Next MDX Remote](https://github.com/hashicorp/next-mdx-remote#react-server-components-rsc--nextjs-app-directory-support)
-   [Tanstack/React Query](https://tanstack.com/query/)
-   [Tailwind CSS](https://tailwindcss.com/)
-   [Shadcn/ui](https://ui.shadcn.com/)
-   [<PERSON>kowalski/Sonner](https://sonner.emilkowal.ski/)
-   [Emilkowalski/Vaul](https://vaul.emilkowal.ski/)


## For Who is launchmvpfast?
This project is for developers who want to build a Software as a Service (SaaS) application. It is designed to expedite the development process of SaaS applications. It is a boilerplate that you can use to build your own SaaS application. It is designed to be easy to use and easy to customize. It is built with Next.js, Drizzle, NextAuth.js, Resend, Uploadthing, Next MDX Remote, Tanstack/React Query, Tailwind CSS, Shadcn/ui, Emilkowalski/Sonner, Emilkowalski/Vaul.

## Why launchmvpfast?
launchmvpfast is an open-source Next.js SaaS Starterkit/Boilerplate designed to expedite the development process of Software as a Service (SaaS) applications. Launch your MVP in days.
