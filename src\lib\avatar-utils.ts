/**
 * Utility functions cho avatar và logo fallbacks
 */

/**
 * Tạo initials từ tên project
 */
export function getProjectInitials(name: string): string {
    return name
        .split(" ")
        .map(word => word[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
}

/**
 * Tạo gradient color dựa trên tên project
 */
export function getProjectGradient(name: string): string {
    const colors = [
        "from-blue-500 to-purple-600",
        "from-green-500 to-blue-600", 
        "from-purple-500 to-pink-600",
        "from-yellow-500 to-orange-600",
        "from-red-500 to-pink-600",
        "from-indigo-500 to-blue-600",
        "from-teal-500 to-green-600",
        "from-orange-500 to-red-600",
        "from-cyan-500 to-blue-600",
        "from-pink-500 to-purple-600",
    ];
    
    // Tạo hash đơn giản từ tên
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
        const char = name.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
    }
    
    const index = Math.abs(hash) % colors.length;
    return colors[index];
}

/**
 * Tạo URL cho DiceBear avatar
 */
export function getDiceBearAvatar(name: string, style: "shapes" | "identicon" | "initials" = "shapes"): string {
    const seed = encodeURIComponent(name.toLowerCase().replace(/\s+/g, "-"));
    return `https://api.dicebear.com/7.x/${style}/svg?seed=${seed}&backgroundColor=transparent`;
}

/**
 * Tạo URL cho GitHub avatar nếu có GitHub URL
 */
export function getGitHubAvatar(githubUrl?: string): string | null {
    if (!githubUrl) return null;
    
    try {
        const url = new URL(githubUrl);
        const pathParts = url.pathname.split('/').filter(Boolean);
        if (pathParts.length >= 1) {
            const owner = pathParts[0];
            return `https://github.com/${owner}.png?size=200`;
        }
    } catch {
        return null;
    }
    
    return null;
}

/**
 * Tạo fallback avatar với nhiều options
 */
export function getProjectAvatar(project: {
    name: string;
    logo?: string | null;
    githubUrl?: string | null;
}): {
    src: string;
    fallback: string;
    gradient: string;
} {
    const initials = getProjectInitials(project.name);
    const gradient = getProjectGradient(project.name);
    
    // Ưu tiên: logo > GitHub avatar > DiceBear
    let src = project.logo || "";
    
    if (!src) {
        const githubAvatar = getGitHubAvatar(project.githubUrl || undefined);
        src = githubAvatar || getDiceBearAvatar(project.name);
    }
    
    return {
        src,
        fallback: initials,
        gradient,
    };
}

/**
 * Tạo màu category dựa trên tên
 */
export function getCategoryColor(categoryName: string): string {
    const colors = [
        "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
        "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
        "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
        "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300",
        "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300",
        "bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300",
    ];
    
    let hash = 0;
    for (let i = 0; i < categoryName.length; i++) {
        const char = categoryName.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    
    const index = Math.abs(hash) % colors.length;
    return colors[index];
}
