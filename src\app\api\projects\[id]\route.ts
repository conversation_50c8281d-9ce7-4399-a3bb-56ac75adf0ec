import { NextRequest, NextResponse } from "next/server";
import { getProjectById } from "@/server/actions/projects";

/**
 * GET /api/projects/[id]
 * <PERSON><PERSON><PERSON> thông tin chi tiết của một project theo ID
 */
export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = params;

        if (!id) {
            return NextResponse.json(
                {
                    success: false,
                    error: "Project ID is required",
                },
                { status: 400 }
            );
        }

        const project = await getProjectById(id);

        if (!project) {
            return NextResponse.json(
                {
                    success: false,
                    error: "Project not found",
                },
                { status: 404 }
            );
        }

        return NextResponse.json({
            success: true,
            data: project,
        });
    } catch (error) {
        console.error(`Error in GET /api/projects/${params.id}:`, error);
        return NextResponse.json(
            {
                success: false,
                error: "Failed to fetch project",
            },
            { status: 500 }
        );
    }
}