"use server";

import { db } from "@/server/db";
import { projectSubmissions, projects } from "@/server/db/schema";
import { adminProcedure } from "@/server/procedures";
import { eq } from "drizzle-orm";
import { revalidateTag } from "next/cache";
import { z } from "zod";
import { redirect } from "next/navigation";
import { siteUrls } from "@/config/urls";

/**
 * Approve a submission and create a project
 */
const approveSubmissionSchema = z.object({
    submissionId: z.string(),
    reviewNotes: z.string().optional(),
});

export async function approveSubmissionAction(
    input: z.infer<typeof approveSubmissionSchema>,
) {
    const { user } = await adminProcedure();
    const { submissionId, reviewNotes } = approveSubmissionSchema.parse(input);

    try {
        // Get the submission
        const submission = await db.query.projectSubmissions.findFirst({
            where: eq(projectSubmissions.id, submissionId),
        });

        if (!submission) {
            throw new Error("Submission not found");
        }

        if (submission.status !== "Pending" && submission.status !== "Under Review") {
            throw new Error("Submission is not in a reviewable state");
        }

        // Create project from submission data
        const projectData = submission.projectData as any;
        const [newProject] = await db
            .insert(projects)
            .values({
                ...projectData,
                submittedBy: submission.submittedBy,
                submittedAt: submission.createdAt,
                reviewedBy: user.id,
                reviewedAt: new Date(),
                status: "Published",
            })
            .returning();

        // Update submission status
        await db
            .update(projectSubmissions)
            .set({
                status: "Approved",
                reviewNotes,
                reviewedBy: user.id,
                reviewedAt: new Date(),
                projectId: newProject.id,
                updatedAt: new Date(),
            })
            .where(eq(projectSubmissions.id, submissionId));

        // Revalidate cache
        revalidateTag("projects");
        revalidateTag("submissions");

        return { success: true, projectId: newProject.id };
    } catch (error) {
        console.error("Error approving submission:", error);
        throw new Error("Failed to approve submission");
    }
}

/**
 * Reject a submission
 */
const rejectSubmissionSchema = z.object({
    submissionId: z.string(),
    reviewNotes: z.string().min(1, "Review notes are required when rejecting"),
});

export async function rejectSubmissionAction(
    input: z.infer<typeof rejectSubmissionSchema>,
) {
    const { user } = await adminProcedure();
    const { submissionId, reviewNotes } = rejectSubmissionSchema.parse(input);

    try {
        // Get the submission
        const submission = await db.query.projectSubmissions.findFirst({
            where: eq(projectSubmissions.id, submissionId),
        });

        if (!submission) {
            throw new Error("Submission not found");
        }

        if (submission.status !== "Pending" && submission.status !== "Under Review") {
            throw new Error("Submission is not in a reviewable state");
        }

        // Update submission status
        await db
            .update(projectSubmissions)
            .set({
                status: "Rejected",
                reviewNotes,
                reviewedBy: user.id,
                reviewedAt: new Date(),
                updatedAt: new Date(),
            })
            .where(eq(projectSubmissions.id, submissionId));

        // Revalidate cache
        revalidateTag("submissions");

        return { success: true };
    } catch (error) {
        console.error("Error rejecting submission:", error);
        throw new Error("Failed to reject submission");
    }
}

/**
 * Request changes for a submission
 */
const requestChangesSchema = z.object({
    submissionId: z.string(),
    reviewNotes: z.string().min(1, "Review notes are required when requesting changes"),
});

export async function requestChangesAction(
    input: z.infer<typeof requestChangesSchema>,
) {
    const { user } = await adminProcedure();
    const { submissionId, reviewNotes } = requestChangesSchema.parse(input);

    try {
        // Get the submission
        const submission = await db.query.projectSubmissions.findFirst({
            where: eq(projectSubmissions.id, submissionId),
        });

        if (!submission) {
            throw new Error("Submission not found");
        }

        if (submission.status !== "Pending" && submission.status !== "Under Review") {
            throw new Error("Submission is not in a reviewable state");
        }

        // Update submission status
        await db
            .update(projectSubmissions)
            .set({
                status: "Needs Changes",
                reviewNotes,
                reviewedBy: user.id,
                reviewedAt: new Date(),
                updatedAt: new Date(),
            })
            .where(eq(projectSubmissions.id, submissionId));

        // Revalidate cache
        revalidateTag("submissions");

        return { success: true };
    } catch (error) {
        console.error("Error requesting changes:", error);
        throw new Error("Failed to request changes");
    }
}

/**
 * Mark submission as under review
 */
export async function markUnderReviewAction(submissionId: string) {
    const { user } = await adminProcedure();

    try {
        // Get the submission
        const submission = await db.query.projectSubmissions.findFirst({
            where: eq(projectSubmissions.id, submissionId),
        });

        if (!submission) {
            throw new Error("Submission not found");
        }

        if (submission.status !== "Pending") {
            throw new Error("Submission is not pending");
        }

        // Update submission status
        await db
            .update(projectSubmissions)
            .set({
                status: "Under Review",
                reviewedBy: user.id,
                updatedAt: new Date(),
            })
            .where(eq(projectSubmissions.id, submissionId));

        // Revalidate cache
        revalidateTag("submissions");

        return { success: true };
    } catch (error) {
        console.error("Error marking submission under review:", error);
        throw new Error("Failed to mark submission under review");
    }
}

/**
 * Bulk approve submissions
 */
export async function bulkApproveSubmissionsAction(submissionIds: string[]) {
    const { user } = await adminProcedure();

    try {
        const results = [];
        
        for (const submissionId of submissionIds) {
            try {
                const result = await approveSubmissionAction({
                    submissionId,
                    reviewNotes: "Bulk approved",
                });
                results.push({ submissionId, success: true, projectId: result.projectId });
            } catch (error) {
                results.push({ submissionId, success: false, error: error.message });
            }
        }

        return { success: true, results };
    } catch (error) {
        console.error("Error bulk approving submissions:", error);
        throw new Error("Failed to bulk approve submissions");
    }
}

/**
 * Bulk reject submissions
 */
export async function bulkRejectSubmissionsAction(
    submissionIds: string[],
    reviewNotes: string,
) {
    const { user } = await adminProcedure();

    try {
        const results = [];
        
        for (const submissionId of submissionIds) {
            try {
                await rejectSubmissionAction({
                    submissionId,
                    reviewNotes,
                });
                results.push({ submissionId, success: true });
            } catch (error) {
                results.push({ submissionId, success: false, error: error.message });
            }
        }

        return { success: true, results };
    } catch (error) {
        console.error("Error bulk rejecting submissions:", error);
        throw new Error("Failed to bulk reject submissions");
    }
}