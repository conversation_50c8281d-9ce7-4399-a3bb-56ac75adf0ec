import { NextRequest, NextResponse } from "next/server";
import { getProjects, searchProjects } from "@/server/actions/projects";

/**
 * GET /api/projects
 * <PERSON><PERSON><PERSON> danh sách tất cả projects hoặc tìm kiếm projects
 * Query params:
 * - search: string (optional) - Từ khóa tìm kiếm
 */
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const searchQuery = searchParams.get("search");

        let projects;
        
        if (searchQuery && searchQuery.trim()) {
            // Tìm kiếm projects theo từ khóa
            projects = await searchProjects(searchQuery.trim());
        } else {
            // Lấy tất cả projects
            projects = await getProjects();
        }

        return NextResponse.json({
            success: true,
            data: projects,
            count: projects.length,
        });
    } catch (error) {
        console.error("Error in GET /api/projects:", error);
        return NextResponse.json(
            {
                success: false,
                error: "Failed to fetch projects",
            },
            { status: 500 }
        );
    }
}