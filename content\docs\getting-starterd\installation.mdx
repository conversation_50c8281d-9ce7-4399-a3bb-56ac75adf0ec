---
title: "Installation"
description: "This content doesn't mean anything it is generated by copilot"
---

## Installation

### Prerequisites

-   Node.js 16.x
-   Yarn 1.x

### Installation

1.  Clone the repository

```bash
git clone https://github.com/alifarooq9/launchmvpfast.git
```

2.  Install dependencies

```bash
yarn install
```

3.  Start the development server

```bash
yarn dev
```

4.  Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

5.  You can start editing the page by modifying `pages/index.tsx`. The page auto-updates as you edit the file.   