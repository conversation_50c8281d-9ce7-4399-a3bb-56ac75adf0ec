"use client";

import { DataTable } from "@/components/data-table/data-table";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { DataTableSearchableColumn } from "@/types/data-table";
import { columns, type Submission } from "./columns";
import { use } from "react";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, Clock, Eye } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface SubmissionsTableProps {
    submissionsPromise: Promise<{
        data: Submission[];
        pageCount: number;
    }>;
}

/**
 * Searchable columns configuration for the submissions table
 */
const searchableColumns: DataTableSearchableColumn<Submission>[] = [
    {
        id: "projectData.name",
        placeholder: "Filter by project name...",
    },
    {
        id: "submitter",
        placeholder: "Filter by submitter...",
    },
];

/**
 * Status options for filtering
 */
const statusOptions = [
    {
        label: "Pending",
        value: "Pending",
        icon: Clock,
        count: 0,
    },
    {
        label: "Under Review",
        value: "Under Review",
        icon: Eye,
        count: 0,
    },
    {
        label: "Approved",
        value: "Approved",
        icon: CheckCircle,
        count: 0,
    },
    {
        label: "Rejected",
        value: "Rejected",
        icon: XCircle,
        count: 0,
    },
    {
        label: "Needs Changes",
        value: "Needs Changes",
        icon: Clock,
        count: 0,
    },
];

export function SubmissionsTable({ submissionsPromise }: SubmissionsTableProps) {
    const { data, pageCount } = use(submissionsPromise);

    return (
        <div className="space-y-4">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
                {statusOptions.map((status) => {
                    const count = data.filter((submission) => submission.status === status.value).length;
                    const Icon = status.icon;
                    
                    return (
                        <div key={status.value} className="rounded-lg border p-4">
                            <div className="flex items-center space-x-2">
                                <Icon className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm font-medium text-muted-foreground">
                                    {status.label}
                                </span>
                            </div>
                            <div className="mt-2">
                                <span className="text-2xl font-bold">{count}</span>
                            </div>
                        </div>
                    );
                })}
            </div>

            {/* Data Table */}
            <DataTable
                columns={columns}
                data={data}
                pageCount={pageCount}
                searchableColumns={searchableColumns}
                newRowLink="/submit"
                deleteRowsAction={async (rows) => {
                    // TODO: Implement bulk delete if needed
                    console.log("Delete rows:", rows);
                }}
            />
        </div>
    );
}