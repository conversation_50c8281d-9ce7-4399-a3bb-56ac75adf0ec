import { NextRequest, NextResponse } from "next/server";
import { getCategories, getProjectsByCategory } from "@/server/actions/projects";

/**
 * GET /api/categories
 * L<PERSON>y danh sách tất cả categories
 * Query params:
 * - withProjects: boolean (optional) - <PERSON><PERSON> lấy kèm projects của category không
 * - categoryId: string (optional) - Lấy projects của category cụ thể
 * - limit: number (optional) - Giới h<PERSON>n số lượng projects trả về
 */
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const withProjects = searchParams.get("withProjects") === "true";
        const categoryId = searchParams.get("categoryId");
        const limit = searchParams.get("limit");
        const limitNumber = limit ? parseInt(limit, 10) : undefined;

        if (categoryId) {
            // Lấy projects của category cụ thể
            const projects = await getProjectsByCategory(categoryId, limitNumber);
            return NextResponse.json({
                success: true,
                data: projects,
                count: projects.length,
            });
        }

        // Lấy danh sách categories
        const categories = await getCategories();

        if (withProjects) {
            // Lấy kèm projects cho mỗi category
            const categoriesWithProjects = await Promise.all(
                categories.map(async (category) => {
                    const projects = await getProjectsByCategory(category.id, limitNumber || 5);
                    return {
                        ...category,
                        projects,
                        projectCount: projects.length,
                    };
                })
            );

            return NextResponse.json({
                success: true,
                data: categoriesWithProjects,
                count: categoriesWithProjects.length,
            });
        }

        return NextResponse.json({
            success: true,
            data: categories,
            count: categories.length,
        });
    } catch (error) {
        console.error("Error in GET /api/categories:", error);
        return NextResponse.json(
            {
                success: false,
                error: "Failed to fetch categories",
            },
            { status: 500 }
        );
    }
}