import { ProjectCard } from "@/app/(web)/_components/project-card";
import { ProjectsGrid } from "@/app/(web)/_components/projects-grid";
import { WebPageWrapper, WebPageHeader } from "@/app/(web)/_components/general-components";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import type { Metadata } from "next";

export const metadata: Metadata = {
    title: "Project Card Demo - AI Tools Directory",
    description: "Demo các variant khác nhau của Project Card component",
};

// Mock data cho demo
const mockProjects = [
    {
        id: "1",
        name: "Discourse",
        slug: "discourse",
        description: "A powerful, customizable platform for building and managing online forums, fostering engaging discussions and collaborative spaces.",
        shortDescription: "Cultivate vibrant online communities with ease",
        githubUrl: "https://github.com/discourse/discourse",
        githubOwner: "discourse",
        githubRepo: "discourse",
        githubStars: 44654,
        githubForks: 8588,
        githubIssues: 0,
        githubLastCommit: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        websiteUrl: "https://discourse.org",
        demoUrl: null,
        documentationUrl: null,
        logo: "https://meta.discourse.org/uploads/default/original/1X/c4218d1c5a936cd5e46b889bb5d68fc433c4d4e4.png",
        screenshots: [],
        categoryId: "1",
        tags: ["forum", "community", "discussion", "discord-alternative", "slack-alternative"],
        primaryLanguage: "Ruby" as const,
        languages: ["Ruby", "JavaScript"],
        license: "GPL-2.0" as const,
        viewCount: 1250,
        clickCount: 340,
        status: "Published" as const,
        isFeatured: true,
        isOpenSource: true,
        submittedBy: null,
        submittedAt: null,
        reviewedBy: null,
        reviewedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        category: "Communication",
        categorySlug: "communication",
    },
    {
        id: "2",
        name: "TensorFlow",
        slug: "tensorflow",
        description: "TensorFlow là một platform machine learning mã nguồn mở end-to-end. Nó có một hệ sinh thái toàn diện và linh hoạt của các công cụ, thư viện và tài nguyên cộng đồng cho phép các nhà nghiên cứu thúc đẩy nghiên cứu ML tiên tiến và các nhà phát triển dễ dàng xây dựng và triển khai các ứng dụng được hỗ trợ bởi ML.",
        shortDescription: "Platform machine learning mã nguồn mở end-to-end từ Google",
        githubUrl: "https://github.com/tensorflow/tensorflow",
        githubOwner: "tensorflow",
        githubRepo: "tensorflow",
        githubStars: 185000,
        githubForks: 74000,
        githubIssues: 2000,
        githubLastCommit: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
        websiteUrl: "https://tensorflow.org",
        demoUrl: null,
        documentationUrl: "https://tensorflow.org/guide",
        logo: "https://www.tensorflow.org/images/tf_logo_social.png",
        screenshots: [],
        categoryId: "2",
        tags: ["machine-learning", "deep-learning", "neural-networks", "python", "google"],
        primaryLanguage: "Python" as const,
        languages: ["Python", "C++", "JavaScript"],
        license: "Apache-2.0" as const,
        viewCount: 15420,
        clickCount: 3240,
        status: "Published" as const,
        isFeatured: true,
        isOpenSource: true,
        submittedBy: null,
        submittedAt: null,
        reviewedBy: null,
        reviewedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        category: "Machine Learning",
        categorySlug: "machine-learning",
    },
    {
        id: "3",
        name: "OpenCV",
        slug: "opencv",
        description: "OpenCV (Open Source Computer Vision Library) là một thư viện phần mềm computer vision và machine learning mã nguồn mở. OpenCV được xây dựng để cung cấp một cơ sở hạ tầng chung cho các ứng dụng computer vision và để tăng tốc việc sử dụng machine perception trong các sản phẩm thương mại. A powerful alternative to proprietary computer vision solutions.",
        shortDescription: "Thư viện Computer Vision mã nguồn mở hàng đầu",
        githubUrl: "https://github.com/opencv/opencv",
        githubOwner: "opencv",
        githubRepo: "opencv",
        githubStars: 78000,
        githubForks: 55000,
        githubIssues: 3500,
        githubLastCommit: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        websiteUrl: "https://opencv.org",
        demoUrl: null,
        documentationUrl: "https://docs.opencv.org",
        logo: null,
        screenshots: [],
        categoryId: "3",
        tags: ["computer-vision", "image-processing", "video-analysis", "c++", "python"],
        primaryLanguage: "C++" as const,
        languages: ["C++", "Python", "Java"],
        license: "Apache-2.0" as const,
        viewCount: 12800,
        clickCount: 2890,
        status: "Published" as const,
        isFeatured: false,
        isOpenSource: true,
        submittedBy: null,
        submittedAt: null,
        reviewedBy: null,
        reviewedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        category: "Computer Vision",
        categorySlug: "computer-vision",
    },
];

export default function DemoPage() {
    return (
        <WebPageWrapper>
            <WebPageHeader
                badge="UI Demo"
                title="Project Card Variants"
            >
                <p className="text-center text-base text-muted-foreground sm:text-lg">
                    Demo các variant khác nhau của Project Card component theo thiết kế OpenAlternative/Discourse
                </p>
            </WebPageHeader>

            {/* Default Grid Layout */}
            <section className="space-y-6">
                <div>
                    <h2 className="text-2xl font-bold mb-2">Default Grid Layout</h2>
                    <p className="text-muted-foreground mb-6">
                        Layout grid mặc định với card đầy đủ thông tin, phù hợp cho trang chủ và category pages
                    </p>
                </div>
                <ProjectsGrid projects={mockProjects} variant="default" />
            </section>

            <Separator className="my-12" />

            {/* Compact List Layout */}
            <section className="space-y-6">
                <div>
                    <h2 className="text-2xl font-bold mb-2">Compact List Layout</h2>
                    <p className="text-muted-foreground mb-6">
                        Layout compact dạng list, phù hợp cho search results và filtered views
                    </p>
                </div>
                <ProjectsGrid projects={mockProjects} variant="compact" />
            </section>

            <Separator className="my-12" />

            {/* Individual Card Examples */}
            <section className="space-y-6">
                <div>
                    <h2 className="text-2xl font-bold mb-2">Individual Card Examples</h2>
                    <p className="text-muted-foreground mb-6">
                        Các ví dụ về từng card riêng lẻ với các trạng thái khác nhau
                    </p>
                </div>

                <div className="grid gap-6 lg:grid-cols-2">
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Default Card với Logo</h3>
                        <ProjectCard project={mockProjects[0]} variant="default" />
                    </div>

                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Compact Card</h3>
                        <ProjectCard project={mockProjects[0]} variant="compact" />
                    </div>

                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Card không có Logo (Fallback)</h3>
                        <ProjectCard project={mockProjects[2]} variant="default" />
                    </div>

                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Featured Project</h3>
                        <ProjectCard project={mockProjects[1]} variant="default" />
                    </div>
                </div>
            </section>

            {/* Features */}
            <section className="space-y-6 mt-12">
                <div>
                    <h2 className="text-2xl font-bold mb-2">Features</h2>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2">Logo Support</Badge>
                            <p className="text-sm text-muted-foreground">
                                Hiển thị logo project hoặc tạo fallback từ tên project
                            </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2">GitHub Stats</Badge>
                            <p className="text-sm text-muted-foreground">
                                Hiển thị stars, forks và thời gian commit cuối
                            </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2">Alternative Info</Badge>
                            <p className="text-sm text-muted-foreground">
                                Hiển thị thông tin "Alternative to" cho các project thay thế
                            </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2">Responsive</Badge>
                            <p className="text-sm text-muted-foreground">
                                Responsive design cho mọi kích thước màn hình
                            </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2">Hover Effects</Badge>
                            <p className="text-sm text-muted-foreground">
                                Smooth hover animations và color transitions
                            </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <Badge className="mb-2">Multiple Variants</Badge>
                            <p className="text-sm text-muted-foreground">
                                Default grid và compact list layouts
                            </p>
                        </div>
                    </div>
                </div>
            </section>
        </WebPageWrapper>
    );
}
