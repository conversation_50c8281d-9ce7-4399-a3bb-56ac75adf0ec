import { StarIcon, GitForkIcon, ClockIcon, AlertCircleIcon } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";

interface GitHubStatsProps {
    stars?: number | null;
    forks?: number | null;
    issues?: number | null;
    lastCommit?: Date | null;
    className?: string;
    variant?: "default" | "compact" | "detailed";
    showLabels?: boolean;
}

/**
 * Component hiển thị GitHub statistics với format đẹp
 * Hỗ trợ nhiều variant và tùy chọn hiển thị
 */
export function GitHubStats({ 
    stars, 
    forks, 
    issues, 
    lastCommit, 
    className,
    variant = "default",
    showLabels = false
}: GitHubStatsProps) {
    const formatNumber = (num: number | null | undefined): string => {
        if (!num) return "0";
        if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
        if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
        return num.toLocaleString();
    };

    const lastUpdate = lastCommit 
        ? formatDistanceToNow(new Date(lastCommit), { addSuffix: true })
        : null;

    if (variant === "compact") {
        return (
            <div className={cn("flex items-center gap-3 text-xs text-muted-foreground", className)}>
                {stars !== null && stars !== undefined && (
                    <div className="flex items-center gap-1">
                        <StarIcon className="h-3 w-3" />
                        <span>{formatNumber(stars)}</span>
                        {showLabels && <span className="hidden sm:inline">stars</span>}
                    </div>
                )}
                {forks !== null && forks !== undefined && (
                    <div className="flex items-center gap-1">
                        <GitForkIcon className="h-3 w-3" />
                        <span>{formatNumber(forks)}</span>
                        {showLabels && <span className="hidden sm:inline">forks</span>}
                    </div>
                )}
                {lastUpdate && (
                    <div className="flex items-center gap-1">
                        <ClockIcon className="h-3 w-3" />
                        <span className="truncate">{lastUpdate}</span>
                    </div>
                )}
            </div>
        );
    }

    if (variant === "detailed") {
        return (
            <div className={cn("grid grid-cols-2 gap-4 p-4 bg-muted/30 rounded-lg", className)}>
                <div className="space-y-3">
                    {stars !== null && stars !== undefined && (
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <StarIcon className="h-4 w-4" />
                                <span>Stars</span>
                            </div>
                            <span className="font-semibold">{formatNumber(stars)}</span>
                        </div>
                    )}
                    {forks !== null && forks !== undefined && (
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <GitForkIcon className="h-4 w-4" />
                                <span>Forks</span>
                            </div>
                            <span className="font-semibold">{formatNumber(forks)}</span>
                        </div>
                    )}
                </div>
                <div className="space-y-3">
                    {issues !== null && issues !== undefined && (
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <AlertCircleIcon className="h-4 w-4" />
                                <span>Issues</span>
                            </div>
                            <span className="font-semibold">{formatNumber(issues)}</span>
                        </div>
                    )}
                    {lastUpdate && (
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <ClockIcon className="h-4 w-4" />
                                <span>Updated</span>
                            </div>
                            <span className="font-semibold text-xs">{lastUpdate}</span>
                        </div>
                    )}
                </div>
            </div>
        );
    }

    // Default variant
    return (
        <div className={cn("flex items-center gap-4 text-sm text-muted-foreground", className)}>
            {stars !== null && stars !== undefined && (
                <div className="flex items-center gap-1">
                    <StarIcon className="h-4 w-4" />
                    <span>{formatNumber(stars)}</span>
                    {showLabels && <span className="hidden sm:inline ml-1">stars</span>}
                </div>
            )}
            {forks !== null && forks !== undefined && (
                <div className="flex items-center gap-1">
                    <GitForkIcon className="h-4 w-4" />
                    <span>{formatNumber(forks)}</span>
                    {showLabels && <span className="hidden sm:inline ml-1">forks</span>}
                </div>
            )}
            {issues !== null && issues !== undefined && (
                <div className="flex items-center gap-1">
                    <AlertCircleIcon className="h-4 w-4" />
                    <span>{formatNumber(issues)}</span>
                    {showLabels && <span className="hidden sm:inline ml-1">issues</span>}
                </div>
            )}
            {lastUpdate && (
                <div className="flex items-center gap-1">
                    <ClockIcon className="h-4 w-4" />
                    <span className="text-xs">{lastUpdate}</span>
                </div>
            )}
        </div>
    );
}

/**
 * Component hiển thị chỉ số stars với animation
 */
export function StarCount({ 
    count, 
    className,
    size = "sm" 
}: { 
    count: number | null | undefined; 
    className?: string;
    size?: "sm" | "md" | "lg";
}) {
    if (!count) return null;

    const sizeClasses = {
        sm: "text-sm",
        md: "text-base", 
        lg: "text-lg",
    };

    const iconSizes = {
        sm: "h-3 w-3",
        md: "h-4 w-4",
        lg: "h-5 w-5",
    };

    return (
        <div className={cn(
            "flex items-center gap-1 text-muted-foreground",
            sizeClasses[size],
            className
        )}>
            <StarIcon className={cn("fill-current", iconSizes[size])} />
            <span className="font-medium">
                {count >= 1000 ? `${(count / 1000).toFixed(1)}k` : count.toLocaleString()}
            </span>
        </div>
    );
}
