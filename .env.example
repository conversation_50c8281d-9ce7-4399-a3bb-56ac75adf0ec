# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Drizzle
DATABASE_URL="postgresql://postgres:password@localhost:5432/demo"

# Next Auth
# You can generate a new secret on the command line with:
# openssl rand -base64 32
# https://next-auth.js.org/configuration/options#secret
NEXTAUTH_SECRET=""
NEXTAUTH_URL="http://localhost:3000"


# Next Auth Google Provider secrets
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Next Auth Github Provider secrets
GITHUB_CLIENT_ID=""
GITHUB_CLIENT_SECRET=""

# Resend Api key
RESEND_API_KEY=""

# Uploadthing keys
UPLOADTHING_SECRET=""
UPLOADTHING_ID=""

# LemonSqueezy keys
LEMONSQUEEZY_API_KEY=""
LEMONSQUEEZY_STORE_ID=""
LEMONSQUEEZY_WEBHOOK_SECRET=""

# Posthog
NEXT_PUBLIC_POSTHOG_KEY=""
NEXT_PUBLIC_POSTHOG_HOST=""