"use server";

import { db } from "@/server/db";
import { projectSubmissions, users } from "@/server/db/schema";
import { adminProcedure } from "@/server/procedures";
import { unstable_noStore as noStore } from "next/cache";
import { asc, count, desc, eq, ilike, inArray, or } from "drizzle-orm";
import { z } from "zod";

/**
 * Get all submissions with pagination and filtering
 * Admin only
 */
const paginatedSubmissionsPropsSchema = z.object({
    page: z.coerce.number().default(1),
    per_page: z.coerce.number().default(10),
    sort: z.string().optional(),
    status: z.string().optional(),
    submitter: z.string().optional(),
    operator: z.string().optional(),
});

type GetPaginatedSubmissionsQueryProps = z.infer<
    typeof paginatedSubmissionsPropsSchema
>;

export async function getAllPaginatedSubmissionsQuery(
    input: GetPaginatedSubmissionsQueryProps,
) {
    noStore();
    await adminProcedure();

    const { page, per_page, sort, status, submitter, operator } = input;

    // Offset to fetch the correct page
    const offset = (page - 1) * per_page;

    const orderBy = sort === "submitter.asc"
        ? asc(users.name)
        : sort === "submitter.desc"
        ? desc(users.name)
        : sort === "status.asc"
        ? asc(projectSubmissions.status)
        : sort === "status.desc"
        ? desc(projectSubmissions.status)
        : sort === "createdAt.asc"
        ? asc(projectSubmissions.createdAt)
        : desc(projectSubmissions.createdAt);

    const where = [
        status ? eq(projectSubmissions.status, status as any) : undefined,
        submitter
            ? operator === "and"
                ? ilike(users.name, `%${submitter}%`)
                : or(
                      ilike(users.name, `%${submitter}%`),
                      ilike(projectSubmissions.submitterEmail, `%${submitter}%`),
                  )
            : undefined,
    ].filter(Boolean);

    const { data, total } = await db.transaction(async (tx) => {
        const data = await tx
            .select({
                id: projectSubmissions.id,
                projectData: projectSubmissions.projectData,
                submittedBy: projectSubmissions.submittedBy,
                submitterEmail: projectSubmissions.submitterEmail,
                submitterName: projectSubmissions.submitterName,
                status: projectSubmissions.status,
                reviewNotes: projectSubmissions.reviewNotes,
                reviewedBy: projectSubmissions.reviewedBy,
                reviewedAt: projectSubmissions.reviewedAt,
                projectId: projectSubmissions.projectId,
                createdAt: projectSubmissions.createdAt,
                updatedAt: projectSubmissions.updatedAt,
                submitter: {
                    id: users.id,
                    name: users.name,
                    email: users.email,
                    image: users.image,
                },
            })
            .from(projectSubmissions)
            .leftJoin(users, eq(projectSubmissions.submittedBy, users.id))
            .where(where.length > 0 ? (where.length === 1 ? where[0] : where.reduce((acc, curr) => curr ? (acc ? operator === "and" ? acc && curr : acc || curr : curr) : acc)) : undefined)
            .orderBy(orderBy)
            .limit(per_page)
            .offset(offset);

        const total = await tx
            .select({ count: count() })
            .from(projectSubmissions)
            .leftJoin(users, eq(projectSubmissions.submittedBy, users.id))
            .where(where.length > 0 ? (where.length === 1 ? where[0] : where.reduce((acc, curr) => curr ? (acc ? operator === "and" ? acc && curr : acc || curr : curr) : acc)) : undefined)
            .then((res) => res[0]?.count ?? 0);

        return { data, total };
    });

    const pageCount = Math.ceil(total / per_page);

    return { data, pageCount };
}

/**
 * Get submission by ID
 * Admin only
 */
export async function getSubmissionByIdQuery(id: string) {
    noStore();
    await adminProcedure();

    return await db.query.projectSubmissions.findFirst({
        where: eq(projectSubmissions.id, id),
        with: {
            submitter: true,
            reviewer: true,
            project: true,
        },
    });
}

/**
 * Get pending submissions count
 * Admin only
 */
export async function getPendingSubmissionsCountQuery() {
    noStore();
    await adminProcedure();

    const result = await db
        .select({ count: count() })
        .from(projectSubmissions)
        .where(eq(projectSubmissions.status, "Pending"));

    return result[0]?.count ?? 0;
}