import { ProjectCard } from "@/app/(web)/_components/project-card";
import { type ProjectWithCategory } from "@/server/actions/projects";
import { cn } from "@/lib/utils";

interface ProjectsGridProps {
    projects: ProjectWithCategory[];
    title?: string;
    description?: string;
    variant?: "default" | "compact";
    columns?: "auto" | 1 | 2 | 3 | 4 | 5;
}

/**
 * Helper function để tạo grid classes dựa trên variant và columns
 */
function getGridClasses(variant: "default" | "compact", columns: "auto" | 1 | 2 | 3 | 4 | 5) {
    if (variant === "compact") {
        return "space-y-4";
    }

    if (columns === "auto") {
        return "grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";
    }

    const columnClasses = {
        1: "grid gap-6 grid-cols-1",
        2: "grid gap-6 grid-cols-1 sm:grid-cols-2",
        3: "grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
        4: "grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
        5: "grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5",
    };

    return columnClasses[columns];
}

/**
 * Component hiển thị danh sách projects trong layout grid
 * Responsive design với số cột thay đổi theo kích thước màn hình
 * Hỗ trợ variant compact cho hiển thị dạng list
 */
export function ProjectsGrid({ projects, title, description, variant = "default", columns = "auto" }: ProjectsGridProps) {
    if (projects.length === 0) {
        return (
            <div className="text-center py-12">
                <div className="mx-auto max-w-md">
                    <div className="mx-auto h-12 w-12 text-muted-foreground">
                        <svg
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 11H5m14-7l2 2-2 2m2-2H9m10 0a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                    <h3 className="mt-4 text-lg font-semibold text-foreground">
                        No projects found
                    </h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                        We couldn't find any AI tools matching your criteria.
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-8">
            {(title || description) && (
                <div className="text-center space-y-2">
                    {title && (
                        <h2 className="text-2xl font-bold tracking-tight">
                            {title}
                        </h2>
                    )}
                    {description && (
                        <p className="text-muted-foreground max-w-2xl mx-auto">
                            {description}
                        </p>
                    )}
                </div>
            )}
            
            <div className={getGridClasses(variant, columns)}>
                {projects.map((project) => (
                    <ProjectCard key={project.id} project={project} variant={variant} />
                ))}
            </div>
        </div>
    );
}