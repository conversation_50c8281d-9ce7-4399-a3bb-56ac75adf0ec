import { Suspense } from "react";
import { notFound } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Icons } from "@/components/ui/icons";
import { ProjectsGrid } from "@/app/(web)/_components/projects-grid";
import { getProjectsByCategory, getCategories } from "@/server/actions/projects";

interface CategoryPageProps {
  params: {
    category: string;
  };
  searchParams: {
    search?: string;
    sort?: string;
    tags?: string;
  };
}

export async function generateMetadata({ params }: CategoryPageProps) {
  const categoryName = decodeURIComponent(params.category);
  return {
    title: `${categoryName} AI Tools - AI Tools Directory`,
    description: `Discover the best ${categoryName} AI tools and projects. Browse our curated collection of artificial intelligence solutions.`,
  };
}

export async function generateStaticParams() {
  const categories = await getCategories();
  return categories.map((category) => ({
    category: encodeURIComponent(category.toLowerCase()),
  }));
}

function CategoryPageSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Skeleton className="h-10 w-64 mb-4" />
        <Skeleton className="h-6 w-96" />
      </div>
      
      <div className="mb-8 space-y-4">
        <div className="flex flex-col md:flex-row gap-4">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="h-64">
            <CardContent className="p-6">
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-full mb-4" />
              <div className="flex gap-2 mb-4">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-20" />
              </div>
              <Skeleton className="h-10 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

function CategoryFilters({ 
  searchParams, 
  category 
}: { 
  searchParams: CategoryPageProps["searchParams"];
  category: string;
}) {
  const availableTags = [
    "Machine Learning",
    "Natural Language Processing",
    "Computer Vision",
    "Deep Learning",
    "Automation",
    "Analytics",
    "API",
    "Open Source",
    "Free",
    "Premium"
  ];

  return (
    <div className="mb-8 space-y-4">
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search Input */}
        <div className="flex-1">
          <Input
            placeholder={`Search ${category} tools...`}
            defaultValue={searchParams.search || ""}
            className="w-full"
          />
        </div>
        
        {/* Sort Select */}
        <Select defaultValue={searchParams.sort || "popular"}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="popular">Most Popular</SelectItem>
            <SelectItem value="newest">Newest</SelectItem>
            <SelectItem value="name">Name A-Z</SelectItem>
            <SelectItem value="rating">Highest Rated</SelectItem>
          </SelectContent>
        </Select>
        
        {/* Filter Button */}
        <Button variant="outline" className="w-32">
          <Icons.plus className="w-4 h-4 mr-2" />
          Filters
        </Button>
      </div>
      
      {/* Tags Filter */}
      <div className="flex flex-wrap gap-2">
        <span className="text-sm font-medium text-muted-foreground mr-2">Tags:</span>
        {availableTags.map((tag) => {
          const isSelected = searchParams.tags?.includes(tag.toLowerCase());
          return (
            <Badge
              key={tag}
              variant={isSelected ? "default" : "outline"}
              className="cursor-pointer hover:bg-primary/10"
            >
              {tag}
            </Badge>
          );
        })}
      </div>
    </div>
  );
}

async function CategoryContent({ params, searchParams }: CategoryPageProps) {
  const categoryName = decodeURIComponent(params.category);
  
  try {
    const projects = await getProjectsByCategory(categoryName, {
      search: searchParams.search,
      sort: searchParams.sort,
      tags: searchParams.tags?.split(","),
    });

    if (!projects || projects.length === 0) {
      return (
        <div className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-4xl font-bold mb-4 capitalize">
              {categoryName} AI Tools
            </h1>
            <p className="text-xl text-muted-foreground">
              Discover the best {categoryName} AI tools and projects
            </p>
          </div>
          
          <CategoryFilters searchParams={searchParams} category={categoryName} />
          
          <div className="text-center py-12">
            <h3 className="text-2xl font-semibold mb-4">No tools found</h3>
            <p className="text-muted-foreground mb-6">
              We couldn't find any {categoryName} tools matching your criteria.
            </p>
            <Button>
              <Icons.plus className="w-4 h-4 mr-2" />
              Submit a Tool
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4 capitalize">
            {categoryName} AI Tools
          </h1>
          <p className="text-xl text-muted-foreground">
            Discover {projects.length} {categoryName} AI tools and projects
          </p>
        </div>
        
        <CategoryFilters searchParams={searchParams} category={categoryName} />
        
        <div className="mb-6 flex justify-between items-center">
          <p className="text-sm text-muted-foreground">
            Showing {projects.length} results
          </p>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Icons.star className="w-4 h-4 mr-2" />
              Save Search
            </Button>
          </div>
        </div>
        
        <ProjectsGrid projects={projects} />
      </div>
    );
  } catch (error) {
    console.error("Error loading category:", error);
    notFound();
  }
}

export default function CategoryPage(props: CategoryPageProps) {
  return (
    <Suspense fallback={<CategoryPageSkeleton />}>
      <CategoryContent {...props} />
    </Suspense>
  );
}