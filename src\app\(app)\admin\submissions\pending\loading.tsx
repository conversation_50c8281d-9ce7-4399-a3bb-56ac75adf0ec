import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { Shell } from "@/components/shell";
import { Skeleton } from "@/components/ui/skeleton";

/**
 * Loading component cho trang pending submissions
 * Hiển thị skeleton loading khi đang tải dữ liệu
 */
export default function PendingSubmissionsLoading() {
    return (
        <Shell variant="sidebar">
            <div className="space-y-6">
                {/* Page Header Skeleton */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div className="space-y-2">
                        <div className="flex items-center gap-2">
                            <Skeleton className="h-8 w-48" />
                            <Skeleton className="h-6 w-24" />
                        </div>
                        <Skeleton className="h-4 w-96" />
                    </div>
                </div>

                {/* Statistics Cards Skeleton */}
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
                    {Array.from({ length: 5 }).map((_, i) => (
                        <div key={i} className="rounded-lg border p-4">
                            <div className="flex items-center space-x-2">
                                <Skeleton className="h-4 w-4" />
                                <Skeleton className="h-4 w-20" />
                            </div>
                            <div className="mt-2">
                                <Skeleton className="h-8 w-8" />
                            </div>
                        </div>
                    ))}
                </div>

                {/* Data Table Skeleton */}
                <DataTableSkeleton
                    columnCount={7}
                    searchableColumnCount={2}
                    filterableColumnCount={1}
                    cellWidths={["10rem", "20rem", "15rem", "10rem", "12rem", "10rem", "8rem"]}
                    shrinkZero
                />
            </div>
        </Shell>
    );
}